package com.wzsec.modules.ic.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.exception.BadRequestException;
import com.wzsec.modules.ic.domain.IcStrategyConfig;
import com.wzsec.modules.ic.service.IcStrategyConfigService;
import com.wzsec.modules.ic.service.IcTaskService;
import com.wzsec.modules.ic.service.dto.IcStrategyConfigQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-21
 */
// @Api(tags = "接口日志检测策略管理")
@RestController
@RequestMapping("/api/icStrategyConfig")
public class IcStrategyConfigController {

    private final IcStrategyConfigService icStrategyConfigService;
    private final IcTaskService icTaskService;

    public IcStrategyConfigController(IcStrategyConfigService icStrategyConfigService, IcTaskService icTaskService) {
        this.icStrategyConfigService = icStrategyConfigService;
        this.icTaskService = icTaskService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    // @PreAuthorize("@el.check('icStrategyConfig:list')")
    public void download(HttpServletResponse response, IcStrategyConfigQueryCriteria criteria) throws IOException {
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        icStrategyConfigService.download(icStrategyConfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口日志检测策略")
    // @ApiOperation("查询接口日志检测策略")
    // @PreAuthorize("@el.check('icStrategyConfig:list')")
    public ResponseEntity<Object> getIcStrategyconfigs(IcStrategyConfigQueryCriteria criteria, Pageable pageable) {
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        return new ResponseEntity<>(icStrategyConfigService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口日志检测策略")
    // @ApiOperation("新增接口日志检测策略")
    // @PreAuthorize("@el.check('icStrategyConfig:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody IcStrategyConfig resources) {
        int count = icStrategyConfigService.findByStrategyname(resources.getStrategyname());
        if (count > 0) {
            throw new BadRequestException("任务名称不能重复");
        }
        return new ResponseEntity<>(icStrategyConfigService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口日志检测策略")
    // @ApiOperation("修改接口日志检测策略")
    // @PreAuthorize("@el.check('icStrategyConfig:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody IcStrategyConfig resources) {
        int count = icStrategyConfigService.findByStrategyname(resources.getStrategyname());
        if (count > 0) {
            if (icStrategyConfigService.findId(resources.getStrategyname()) != resources.getId()) {
                throw new BadRequestException("任务名称不能重复");
            }
        }
        icStrategyConfigService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口日志检测策略")
    // @ApiOperation("删除接口日志检测策略")
    // @PreAuthorize("@el.check('icStrategyConfig:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        for (Integer id : ids) {
            int count = icTaskService.getByStrategyConfIg(id.toString());
            if (count > 0) {
                throw new BadRequestException("删除的策略中有正被在使用的策略，请先删除相关任务");
            }
        }
        icStrategyConfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/getStrategysByType/{type}")
    @Log("根据策略类型查询策略")
    // @ApiOperation("根据策略类型查询策略")
    public ResponseEntity<Object> getStrategysByType(@PathVariable String type) {
        return new ResponseEntity<>(icStrategyConfigService.getStrategysByType(type), HttpStatus.OK);
    }

    @GetMapping("/queryInterfacePolicy/{type}")
    @Log("查询接口策略")
    public ResponseEntity<Object> queryInterfacePolicy(@PathVariable String type) {
        return new ResponseEntity<>(icStrategyConfigService.queryInterfacePolicy(type), HttpStatus.OK);
    }

    // @ApiOperation("返回全部的策略")
    @GetMapping(value = "/getStrategys")
    // @PreAuthorize("@el.check('icStrategyConfig:list')")
    public ResponseEntity<Object> getStrategys() {
        return new ResponseEntity<>(icStrategyConfigService.getStrategys(), HttpStatus.OK);
    }

}
