package com.wzsec.modules.ic.repository;

import com.wzsec.modules.ic.domain.ApiUnusedak;
import com.wzsec.modules.ic.domain.ApiUnusedcheckresult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface ApiUnusedakRepository extends JpaRepository<ApiUnusedak, Integer>, JpaSpecificationExecutor<ApiUnusedak> {

    /**
     * 查询最新时间
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link String}
     */
    @Query(value = "SELECT MAX(checktime) FROM sdd_api_unusedak WHERE checktime BETWEEN :beginDate AND :endDate ", nativeQuery = true)
    String queryLatestTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 查询最新时间
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link String}
     */
    @Query(value = "SELECT MAX(checktime) FROM sdd_api_unusedak WHERE checktime BETWEEN :beginDate AND :endDate AND apicode IN (:domainApiCodeList) ", nativeQuery = true)
    String queryLatestTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);

    /**
     * 查询最近不常调用的应用信息
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    @Query(value = "SELECT * FROM sdd_api_unusedak WHERE checktime BETWEEN :beginDate AND :endDate ORDER BY checktime, risk DESC", nativeQuery = true)
    List<ApiUnusedak> queryLatestUnusedCheckResult(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 查询数量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    @Query(value = "SELECT COUNT(DISTINCT ak) FROM sdd_api_unusedak WHERE checktime BETWEEN :beginDate AND :endDate ", nativeQuery = true)
    int queryQuantity(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 查询数量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    @Query(value = "SELECT COUNT(DISTINCT ak) FROM sdd_api_unusedak WHERE checktime BETWEEN :beginDate AND :endDate AND apicode IN (:domainApiCodeList)", nativeQuery = true)
    int queryQuantity(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);


}