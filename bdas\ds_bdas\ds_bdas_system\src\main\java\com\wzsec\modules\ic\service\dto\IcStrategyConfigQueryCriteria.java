package com.wzsec.modules.ic.service.dto;

import com.wzsec.annotation.Query;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-21
 */
@Data
public class IcStrategyConfigQueryCriteria {

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object apicode;

    @Query
    private String strategytype;

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createtime;

    @Query(blurry = "strategyname,strategydes,apicode,apimethod,incheckrule,outcheckrule")
    private String blurry;
}
