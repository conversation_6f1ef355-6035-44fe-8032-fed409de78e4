package com.wzsec.modules.ic.service.impl;

import com.wzsec.modules.ic.domain.ApiInfrequentlycall;
import com.wzsec.modules.ic.domain.enums.RiskEnum;
import com.wzsec.modules.ic.repository.ApiInfrequentlycallRepository;
import com.wzsec.modules.ic.service.ApiInfrequentlycallService;
import com.wzsec.modules.ic.service.dto.ApiInfrequentlycallDto;
import com.wzsec.modules.ic.service.dto.ApiInfrequentlycallQueryCriteria;
import com.wzsec.modules.ic.service.mapper.ApiInfrequentlycallMapper;
import com.wzsec.utils.FileUtil;
import com.wzsec.utils.PageUtil;
import com.wzsec.utils.QueryHelp;
import com.wzsec.utils.ValidationUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
@Service
//@CacheConfig(cacheNames = "apiInfrequentlycall")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class ApiInfrequentlycallServiceImpl implements ApiInfrequentlycallService {

    private final ApiInfrequentlycallRepository apiInfrequentlycallRepository;

    private final ApiInfrequentlycallMapper apiInfrequentlycallMapper;

    public ApiInfrequentlycallServiceImpl(ApiInfrequentlycallRepository apiInfrequentlycallRepository,
                                          ApiInfrequentlycallMapper apiInfrequentlycallMapper) {
        this.apiInfrequentlycallRepository = apiInfrequentlycallRepository;
        this.apiInfrequentlycallMapper = apiInfrequentlycallMapper;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(ApiInfrequentlycallQueryCriteria criteria, Pageable pageable) {
        Page<ApiInfrequentlycall> page = apiInfrequentlycallRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(apiInfrequentlycallMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<ApiInfrequentlycallDto> queryAll(ApiInfrequentlycallQueryCriteria criteria) {
        List<ApiInfrequentlycallDto> dto = apiInfrequentlycallMapper.toDto(apiInfrequentlycallRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
        return dto;
    }

    @Override
    //@Cacheable(key = "#p0")
    public ApiInfrequentlycallDto findById(Integer id) {
        ApiInfrequentlycall apiInfrequentlycall = apiInfrequentlycallRepository.findById(id).orElseGet(ApiInfrequentlycall::new);
        ValidationUtil.isNull(apiInfrequentlycall.getId(), "ApiInfrequentlycall", "id", id);
        return apiInfrequentlycallMapper.toDto(apiInfrequentlycall);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public ApiInfrequentlycallDto create(ApiInfrequentlycall resources) {
        return apiInfrequentlycallMapper.toDto(apiInfrequentlycallRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(ApiInfrequentlycall resources) {
        ApiInfrequentlycall apiInfrequentlycall = apiInfrequentlycallRepository.findById(resources.getId()).orElseGet(ApiInfrequentlycall::new);
        ValidationUtil.isNull(apiInfrequentlycall.getId(), "ApiInfrequentlycall", "id", resources.getId());
        apiInfrequentlycall.copy(resources);
        apiInfrequentlycallRepository.save(apiInfrequentlycall);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            apiInfrequentlycallRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ApiInfrequentlycallDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        int size = 0;
        for (ApiInfrequentlycallDto apiInfrequentlycall : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("接口编码", apiInfrequentlycall.getApicode());
            map.put("接口URL", apiInfrequentlycall.getApiurl());
            map.put("接口名称", apiInfrequentlycall.getSparefield3());
            map.put("当日调用次数", apiInfrequentlycall.getCallcount());
            map.put("风险程度", RiskEnum.getRisk(apiInfrequentlycall.getRisk()));
            map.put("检测时间", apiInfrequentlycall.getChecktime());
            list.add(map);
            size = map.size();
        }
        FileUtil.downloadExcel(list, response, "不常调用API检测结果", size);
    }

    @Override
    public List<ApiInfrequentlycall> queryContentResult(String beginDate, String endDate) {
        return apiInfrequentlycallRepository.queryContentResult(beginDate, endDate);
    }

    @Override
    public String queryLatestTime(String beginDate, String endDate) {
        return apiInfrequentlycallRepository.queryLatestTime(beginDate, endDate);
    }

    @Override
    public String queryLatestTime(String beginDate, String endDate, List<String> domainApiCodeList) {
        return apiInfrequentlycallRepository.queryLatestTime(beginDate, endDate, domainApiCodeList);
    }

    @Override
    public List<String> queryLatestInfrequentlyCall(String beginDate, String endDate) {
        return apiInfrequentlycallRepository.queryLatestInfrequentlyCall(beginDate, endDate);
    }

    @Override
    public List<String> queryLatestInfrequentlyCall(String beginDate, String endDate, List<String> domainApiCodeList) {
        return apiInfrequentlycallRepository.queryLatestInfrequentlyCall(beginDate, endDate,domainApiCodeList);
    }

}