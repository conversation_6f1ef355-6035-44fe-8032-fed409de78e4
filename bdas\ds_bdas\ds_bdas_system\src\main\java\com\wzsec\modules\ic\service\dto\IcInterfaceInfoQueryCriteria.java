package com.wzsec.modules.ic.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

import com.wzsec.annotation.Query;

/**
 * <AUTHOR>
 * @date 2021-04-15
 */
@Data
public class IcInterfaceInfoQueryCriteria {

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object apicode;

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createtime;

    @Query
    private String apiname;

    @Query
    private String sparefield2;

    @Query
    private String sparefield3;

    @Query
    private String syncstate;

    @Query
    private String interfaceStatus;

    //表标签
    private List<Long> labelValue;

    @Query(blurry = "apicode,apiname,inparamMean,outparamMean,area,dataSource,registerUnit,sparefield2,syncstate,sparefield3")
    private String blurry;
}
