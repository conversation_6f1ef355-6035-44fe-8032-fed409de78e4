package com.wzsec.modules.ic.service.dto;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

import com.wzsec.annotation.Query;

/**
 * <AUTHOR>
 * @date 2022-10-27
 */
@Data
public class IcApiauditreportQueryCriteria {

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object apicode;

    private Timestamp dateStart;

    private Timestamp dateEnd;

    @Query(type = Query.Type.IN)
    private List<String> taskname;

    @Query(type = Query.Type.BETWEEN)
    private List<String> createtime;

    @Query(blurry = ",")
    private String blurry;

    /*email标识符*/
    private boolean exportNot;
}
