package com.wzsec.modules.quartz.config;

import cn.hutool.extra.mail.Mail;
import cn.hutool.extra.mail.MailAccount;
import com.wzsec.aop.log.Log;
import com.wzsec.exception.BadRequestException;
import com.wzsec.modules.ic.service.IcAlarmdisposalService;
import com.wzsec.modules.ic.service.IcApiauditreportService;
import com.wzsec.modules.ic.service.dto.IcAlarmdisposalQueryCriteria;
import com.wzsec.modules.ic.service.dto.IcApiauditreportQueryCriteria;
import com.wzsec.utils.Const;
import com.wzsec.utils.DateUtils;
import com.wzsec.utils.DomainUtil;
import com.wzsec.utils.EncryptUtils;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-06-24
 */
@Slf4j
@Configuration
@Service
public class EmailConfigJob implements Job {

    @Autowired
    private IcApiauditreportService icApiauditreportService;

    @Autowired
    private IcAlarmdisposalService icAlarmdisposalService;

//    static String interfaceAlarm = "接口检测告警";


    @Log("执行定时任务")
    @Override
    public void execute(JobExecutionContext context) {
        JobDataMap jobDataMap = context.getJobDetail().getJobDataMap();
        String jobReceiver = jobDataMap.getString("jobReceiver");//收件人
        String jobUser = jobDataMap.getString("jobUser");//用户名
        String jobHost = jobDataMap.getString("jobHost");//SMTP地址
        String jobPort = jobDataMap.getString("jobPort");//SMTP端口
        String jobPass = jobDataMap.getString("jobPass");//SMTP密码
        String jobFromUser = jobDataMap.getString("jobFromUser");//发件人
        String jobChecktime = jobDataMap.getString("jobChecktime");//定时时间
        String jobMotif = jobDataMap.getString("jobMotif");//定时时间

        List<String> list = new ArrayList<>();
        //判断收件人是否存在多个
        if (jobReceiver.contains(",")) {
            list = Arrays.asList(jobReceiver.split(","));
        } else {
            list.add(jobReceiver);
        }
        String tempPath = ""; //附件路径
        String content = ""; //内容
        String interfaceAlarm = "接口检测告警";

        //判断周报,日报
        if (Const.EMAIL_TITLEONE.equals(jobMotif)) {
            //从配置文件获取审计报告时间
            String beginDate = "";

            String[] split = jobChecktime.split("\\s+");

            String day = split[3].trim(); //日期
            String month = split[4].trim(); //月份
            String week = split[5].trim(); //星期

            if (month.equals("*")) { //每个月
                if (!day.equals("*") && !day.equals("?")) {
                    beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 30);//获取一个月
                } else {
                    if (!week.equals("?")) {
                        beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 7);//获取每周
                    } else {
                        beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 1);//获取每天
                    }
                }
            } else {
                if (month.contains("1") || month.contains("15") || month.contains("L")) { //获取半个月
                    beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 15);//获取半个月
                } else { //获取一个月
                    beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 30);//获取一个月
                }
            }

            //if (Const.CRON_EXPRESSION_MONTHLY.equals(jobChecktime)) {
            //    beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 30);//每月
            //} else if (Const.CRON_EXPRESSION_HALFAMONTH.equals(jobChecktime)) {
            //    beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 15);//半个月
            //} else if (Const.CRON_EXPRESSION_EVERYWEEK.equals(jobChecktime)) {
            //    beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 7);//每周
            //} else if (Const.CRON_EXPRESSION_EVERY_DAY.equals(jobChecktime)) {
            //    beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 1);//每天
            //} else {
            //    beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 7);//每周
            //}

            beginDate = beginDate + " 00:00:00";

            String endDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 0) + " 23:59:59";//结束时间

            HttpServletResponse response = null;
            try {
                IcApiauditreportQueryCriteria criteria = new IcApiauditreportQueryCriteria();
                criteria.setDateStart(Timestamp.valueOf(beginDate));
                criteria.setDateEnd(Timestamp.valueOf(endDate));
                criteria.setExportNot(true);
                //获取审计报告路径
                // 根据申请部门名称分域
                List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
                tempPath = icApiauditreportService.downloadApiAuditReport(criteria, response, domainApiCodeList);
            } catch (Exception e) {
                e.printStackTrace();
            }

            //内容
//            int silentAccountSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_SILENT_ACCOUNT_BURST_ACCESS);
//            int apiUserCallSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_USER_VISITS_ARE_HIGH);
//            int invokingrecordSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_VISITS_ARE_HIGH);
//            int infrequentlycallSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_SUDDEN_ACCESS_LONG_NON_VISITED);
//            int nonperiodcallSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_VISITS_DURING_ABNORMAL);
//            int unusedCheckSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_NO_CALL_VOLUME);
//            int errorCheckSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_CALL_ERROR);
//            int apiAttackDetectionSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_ATTACK_DETECTION);
//            int sensitiveSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_SENSITIVE_DATA);
//            int unexpectedSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_UNEXPECTED_CONTENTS);
//            int outdatacallSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_BURSTS_ACQUIRE_LARGE_DATA);
//            int apiAuthCheckSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_INTERFACE_AUTHENTICATION);
//            int apiComplianceAuditSum = icAlarmdisposalService.scenarioAlarmAmount(beginDate, endDate, Const.DICT_API_COMPLIANCEAUDIT);
//            int sumRisk = sensitiveSum + unexpectedSum + outdatacallSum + apiAuthCheckSum + apiComplianceAuditSum +
//                    silentAccountSum + apiUserCallSum + invokingrecordSum + infrequentlycallSum + nonperiodcallSum + unusedCheckSum + errorCheckSum + apiAttackDetectionSum;

            int sumRisk = icAlarmdisposalService.queryTotalNumberAlarms(beginDate, endDate);
            int handledAlarm = icAlarmdisposalService.queryAlarmDisposalNum(beginDate, endDate, Const.INTERFACE_ALARM_DISPOSAL_PROCESSED);//已处置告警
            int unhandledAlarm = icAlarmdisposalService.queryAlarmDisposalNum(beginDate, endDate, Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED);//未处置告警
            int ignoredAlarm = icAlarmdisposalService.queryAlarmDisposalNum(beginDate, endDate, Const.INTERFACE_ALARM_DISPOSAL_IGNORED);//已忽略告警
            int hightRiskNum = icAlarmdisposalService.queryAlarmAmount(beginDate, endDate, Const.RISK_HIGH);//高危告警
            int middleNum = icAlarmdisposalService.queryAlarmAmount(beginDate, endDate, Const.RISK_MIDDLE);//低位告警
            //周期内共发现告警0个，已处置0个，未处置0个，已忽略0个。分为3级告警，其中高危告警0个，中危告警0个。详情请查看附件
            content = "周期内共发现告警" + sumRisk + "个，已处置" + handledAlarm + "个，未处置" + unhandledAlarm + "个，已忽略" + ignoredAlarm + "个。分为3级告警，其中高危告警" + hightRiskNum + "个，中危告警" + middleNum + "个。详情请查看附件";
        } else if (Const.EMAIL_TITLETWO.equals(jobMotif)) {
            String date = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 0);//时间
            IcAlarmdisposalQueryCriteria criteria = new IcAlarmdisposalQueryCriteria();
            criteria.setDateStart(Timestamp.valueOf(date + " 00:00:00"));
            criteria.setDateEnd(Timestamp.valueOf(date + " 23:59:59"));
            try {
                tempPath = icAlarmdisposalService.downloadMailExportExcel(icAlarmdisposalService.queryAll(criteria));
                content = "周期内接口检测告警结果进行推送，详情请查看附件。";
            } catch (IOException e) {
                e.printStackTrace();
            }
        }


        // 封装
        MailAccount account = new MailAccount();
        account.setHost(jobHost);
        account.setPort(Integer.parseInt(jobPort));
        account.setAuth(true);
        try {
            // 对称解密
            account.setPass(EncryptUtils.desDecrypt(jobPass));
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
        account.setFrom(jobUser + "<" + jobFromUser + ">");
        // ssl方式发送
        account.setSslEnable(true);

        // 发送
        try {
            DataSource dataSource = new FileDataSource(new File(tempPath));
            int size = list.size();
            Mail.create(account)
                    .setTos(list.toArray(new String[size]))
                    .setTitle(interfaceAlarm)
                    .setContent(content)
                    .setHtml(true)
                    //关闭session
                    .setUseGlobalSession(false)
                    .setAttachments(dataSource)//附件
                    .send();
        } catch (Exception e) {
            e.getMessage();
        }

        File file = new File(tempPath);

        // 终止后删除临时文件
        boolean delete = file.delete();
        if (delete) {
            log.info("邮件发送成功,已对本地临时文件进行删除");
        }

    }
}
