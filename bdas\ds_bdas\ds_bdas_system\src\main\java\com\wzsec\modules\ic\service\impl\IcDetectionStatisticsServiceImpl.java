package com.wzsec.modules.ic.service.impl;

import cn.hutool.core.lang.Console;
import com.alipay.api.domain.Person;
import com.wzsec.modules.ic.domain.IcInvokingRecord;
import com.wzsec.modules.ic.service.IcAlarmdisposalService;
import com.wzsec.modules.ic.service.IcDetectionStatisticsService;
import com.wzsec.modules.ic.service.IcInvokingRecordService;
import com.wzsec.modules.ic.service.IcResultDetailService;
import com.wzsec.utils.Const;
import com.wzsec.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.wzsec.utils.DateUtils.parseFormatDateToDay;

/**
 * <AUTHOR>
 * @date 2022-11-01
 */
@Service
@Slf4j
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class IcDetectionStatisticsServiceImpl implements IcDetectionStatisticsService {

    @Autowired
    private IcAlarmdisposalService icAlarmdisposalService;  //告警处置

    @Autowired
    private IcResultDetailService icResultDetailService;   //接口输出敏感数据

    @Autowired
    private IcInvokingRecordService icInvokingRecordService;


    /**
     * 接口调用趋势图TOP5(7日平均调用量排序)
     *
     * @return {@link Object}
     */
    @Override
    public Object interfaceInvocationTrendDiagram(String beginDate, String endDate) {
        Map<String, Object> interfaceWarnTendency = new HashMap<>();

        List<Map<String, Object>> interfaceTendencyList = new ArrayList<>();

        Console.log("获取{} - {}范围类接口趋势图....", beginDate, endDate);
        List<String> everyDayList = DateUtils.findEveryDay(beginDate, endDate);

        //取值TOP5
        Date begin = parseFormatDateToDay(beginDate);
        Date end = parseFormatDateToDay(endDate);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<String> interfaceTopList = icInvokingRecordService.querySevenDayMeanValueTOP(simpleDateFormat.format(begin), simpleDateFormat.format(end));

        if (interfaceTopList.size() > 0) {
//            interfaceTopList.sort(new Comparator<IcInvokingRecord>() {
//                @Override
//                public int compare(IcInvokingRecord o1, IcInvokingRecord o2) {
//                    return Math.toIntExact(o2.getNum() - o1.getNum());
//                }
//            });
//            if (interfaceTopList.size() > 5) {
//                interfaceTopList = interfaceTopList.subList(0, 5);
//            }
            for (String icInvoking : interfaceTopList) {

                Map<String, Object> adjustmentOfDosageTendencyMap = new HashMap<>();
                List<Map<String, Object>> volumeRequestsDataList = new ArrayList<>();

                List<Map<String, Object>> maps = icInvokingRecordService.queryDailyCall(beginDate, endDate, icInvoking);
                for (Map<String, Object> alarmAmountMap : maps) {
                    Object date = alarmAmountMap.get("date") == null ? "" : alarmAmountMap.get("date");
                    Object num = alarmAmountMap.get("num") == null ? 0 : alarmAmountMap.get("num");
                    Map<String, Object> volumeRequestsDataMap = new HashMap<>();
                    volumeRequestsDataMap.put("date", date);
                    volumeRequestsDataMap.put("volumeRequests", num);
                    volumeRequestsDataList.add(volumeRequestsDataMap);
                }
//
//
//
//                for (int i = 0; i < everyDayList.size(); i++) {
//                    String theSameDay = everyDayList.get(i);
//                    String icInvokingRecord = icInvokingRecordService.queryDailyCallSituation(theSameDay, icInvoking);
//
//                    if (icInvokingRecord != null) {
//                        Map<String, Object> volumeRequestsDataMap = new HashMap<>();
//                        volumeRequestsDataMap.put("date", theSameDay);
//                        volumeRequestsDataMap.put("volumeRequests", icInvokingRecord);
//                        volumeRequestsDataList.add(volumeRequestsDataMap);
//                    }
//                }

                adjustmentOfDosageTendencyMap.put("label", icInvoking); //接口编码
                adjustmentOfDosageTendencyMap.put("type", "volumeRequests"); //调用量
                adjustmentOfDosageTendencyMap.put("result", volumeRequestsDataList);
                interfaceTendencyList.add(adjustmentOfDosageTendencyMap);

            }
        }

        interfaceWarnTendency.put("data", interfaceTendencyList);
        return interfaceWarnTendency;

    }

    /**
     * 告警处置图
     *
     * @return {@link Object}
     */
    @Override
    public Object alarmHandlingDiagram(String beginDate, String endDate) {

        Map<String, Object> alarmHandlingDiagram = new HashMap<>();

        Console.log("获取{} - {}范围类告警处置图....", beginDate, endDate);

        List<Map<String, Object>> alarmHandlingList = new ArrayList<>();
        Map<String, Object> highRiskAlarmMap = new HashMap<>();
        Map<String, Object> middleRiskAlarmMap = new HashMap<>();
        Map<String, Object> lowRiskAlarmMap = new HashMap<>();

        int highRiskNum = icAlarmdisposalService.queryAlarmAmount(beginDate, endDate, Const.RISK_HIGH);
        int highRiskNotDisposed = icAlarmdisposalService.queryRiskAlarmDisposalNum(beginDate, endDate, Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED, Const.RISK_HIGH);
        int middleNum = icAlarmdisposalService.queryAlarmAmount(beginDate, endDate, Const.RISK_MIDDLE);
        int middleRiskNotDisposed = icAlarmdisposalService.queryRiskAlarmDisposalNum(beginDate, endDate, Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED, Const.RISK_MIDDLE);
        int lowNum = icAlarmdisposalService.queryAlarmAmount(beginDate, endDate, Const.RISK_LOW);
        int lowRiskNotDisposed = icAlarmdisposalService.queryRiskAlarmDisposalNum(beginDate, endDate, Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED, Const.RISK_LOW);

        highRiskAlarmMap.put("label", Const.SEVERITY_HIGH);
        highRiskAlarmMap.put("value", "highRisk");
        highRiskAlarmMap.put("alarmQuantity", highRiskNum);
        highRiskAlarmMap.put("notDisposed", highRiskNotDisposed);
        alarmHandlingList.add(highRiskAlarmMap);

        middleRiskAlarmMap.put("label", Const.SEVERITY_MIDDLE);
        middleRiskAlarmMap.put("value", "middleRisk");
        middleRiskAlarmMap.put("alarmQuantity", middleNum);
        middleRiskAlarmMap.put("notDisposed", middleRiskNotDisposed);
        alarmHandlingList.add(middleRiskAlarmMap);

        lowRiskAlarmMap.put("label", Const.SEVERITY_LOW);
        lowRiskAlarmMap.put("value", "lowRisk");
        lowRiskAlarmMap.put("alarmQuantity", lowNum);
        lowRiskAlarmMap.put("notDisposed", lowRiskNotDisposed);
        alarmHandlingList.add(lowRiskAlarmMap);

        alarmHandlingDiagram.put("result", alarmHandlingList);

        return alarmHandlingDiagram;
    }

    /**
     * 敏感数据流向图
     *
     * @return {@link Object}
     */
    @Override
    public Object sensitiveDataFlowChart(String beginDate, String endDate) {
        Map<String, Object> sensitiveDataFlowChart = new HashMap<>();

        List<Object> axleList = new ArrayList<>();
        Set<String> dataSet = new HashSet<>();

        Console.log("获取{} - {}范围类敏感数据流向图....", beginDate, endDate);

        List<Object[]> sensitiveTypeList = icResultDetailService.queryInterfacesSensitiveTypesSum(beginDate, endDate);

        Set<String> alarmTypeSet = new HashSet<>();
        List<Object> linksList = new ArrayList<>();

        for (Object[] objects : sensitiveTypeList) {
            String apicode = (String) objects[0]; //接口编码
            String resulttype = (String) objects[1]; //告警类型
            BigInteger bi = new BigInteger(String.valueOf(objects[2]));
            String sensitiveQuantity = bi.toString();  //告警类型数量

            Map<String, Object> districtInterfaceMap = new HashMap<>();

            districtInterfaceMap.put("source", "API服务"); // TODO 固定值
            districtInterfaceMap.put("target", apicode); //接口编码
            districtInterfaceMap.put("value", 5); // TODO 前端暂定默认给值5
            linksList.add(districtInterfaceMap);
            dataSet.add(apicode);
            dataSet.add("API服务");

            if (StringUtils.isNotBlank(apicode) && StringUtils.isNotBlank(resulttype)) {
                dataSet.add(apicode);
                alarmTypeSet.add(resulttype);
                Map<String, Object> sensitiveDataAndQuantityMap = new HashMap<>();
                sensitiveDataAndQuantityMap.put("source", apicode);
                sensitiveDataAndQuantityMap.put("target", resulttype);
                sensitiveDataAndQuantityMap.put("value", sensitiveQuantity);
                linksList.add(sensitiveDataAndQuantityMap);

            }
        }

        // 敏感类型信息
        for (String alarmType : alarmTypeSet) {
            if (StringUtils.isNotBlank(alarmType)) {
                dataSet.add(alarmType);
            }
        }
        for (String data : dataSet) {
            Map<String, String> axleMap = new HashMap<>();
            axleMap.put("name", data);
            axleList.add(axleMap);
        }

        sensitiveDataFlowChart.put("data", axleList);
        sensitiveDataFlowChart.put("links", linksList);
        return sensitiveDataFlowChart;
    }


    /**
     * 告警级别趋势图
     *
     * @return {@link Object}
     */
    @Override
    public Object alarmTrendDiagram(String beginDate, String endDate) {
        Map<String, Object> alarmTrendDiagramTendency = new HashMap<>();

        List<Map<String, Object>> alarmTrendDiagramList = new ArrayList<>();

        Console.log("获取{} - {}范围内告警级别趋势图....", beginDate, endDate);
        List<String> everyDayList = DateUtils.findEveryDay(beginDate, endDate);

        List<Map<String, Object>> alarmTrendList1 = new ArrayList<>();
        List<Map<String, Object>> alarmTrendList2 = new ArrayList<>();
        List<Map<String, Object>> alarmTrendList3 = new ArrayList<>();
//        for (int i = 0; i < everyDayList.size(); i++) {
//            Map<String, Object> alarmMap1 = new HashMap<>();
//            Map<String, Object> alarmMap2 = new HashMap<>();
//            Map<String, Object> alarmMap3 = new HashMap<>();
//
//            String theSameDay = everyDayList.get(i);
//            int highRiskNum = icAlarmdisposalService.queryAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.RISK_HIGH);
//            int middleNum = icAlarmdisposalService.queryAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.RISK_MIDDLE);
//            int lowNum = icAlarmdisposalService.queryAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.RISK_LOW);
//
//            alarmMap1.put("data", theSameDay);
//            alarmMap1.put("alarmQuantity", highRiskNum);
//            alarmTrendList1.add(alarmMap1);
//
//            alarmMap2.put("data", theSameDay);
//            alarmMap2.put("alarmQuantity", middleNum);
//            alarmTrendList2.add(alarmMap2);
//
//            alarmMap3.put("data", theSameDay);
//            alarmMap3.put("alarmQuantity", lowNum);
//            alarmTrendList3.add(alarmMap3);
//        }
        List<Map<String, Object>> lowMaps = icAlarmdisposalService.queryRiskAlarm(beginDate, endDate, Const.RISK_HIGH);
        for (Map<String, Object> lowMap : lowMaps) {
            Object cktime = lowMap.get("cktime") == null ? "" : lowMap.get("cktime");
            Object num = lowMap.get("reservefield6") == null ? "0" : lowMap.get("reservefield6");
            Map<String, Object> alarmMap1 = new HashMap<>();
            alarmMap1.put("data", cktime);
            alarmMap1.put("alarmQuantity", num);
            alarmTrendList1.add(alarmMap1);
        }
        List<Map<String, Object>> middleMaps = icAlarmdisposalService.queryRiskAlarm(beginDate, endDate, Const.RISK_MIDDLE);
        for (Map<String, Object> middleMap : middleMaps) {
            Object cktime = middleMap.get("cktime") == null ? "" : middleMap.get("cktime");
            Object num = middleMap.get("reservefield6") == null ? "0" : middleMap.get("reservefield6");
            Map<String, Object> alarmMap2 = new HashMap<>();
            alarmMap2.put("data", cktime);
            alarmMap2.put("alarmQuantity", num);
            alarmTrendList2.add(alarmMap2);
        }
        List<Map<String, Object>> highRiskMaps = icAlarmdisposalService.queryRiskAlarm(beginDate, endDate, Const.RISK_LOW);
        for (Map<String, Object> highRiskMap : highRiskMaps) {
            Map<String, Object> alarmMap3 = new HashMap<>();
            Object cktime = highRiskMap.get("cktime") == null ? "" : highRiskMap.get("cktime");
            Object num = highRiskMap.get("reservefield6") == null ? "0" : highRiskMap.get("reservefield6");
            alarmMap3.put("data", cktime);
            alarmMap3.put("alarmQuantity", num);
            alarmTrendList3.add(alarmMap3);
        }

        Map<String, Object> alarmTrendDiagramMap1 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap2 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap3 = new HashMap<>();

        alarmTrendDiagramMap1.put("label", "高危");
        alarmTrendDiagramMap1.put("type", "alarmQuantity"); //调用量
        alarmTrendDiagramMap1.put("result", alarmTrendList1);
        alarmTrendDiagramList.add(alarmTrendDiagramMap1);

        alarmTrendDiagramMap2.put("label", "中危");
        alarmTrendDiagramMap2.put("type", "alarmQuantity"); //调用量
        alarmTrendDiagramMap2.put("result", alarmTrendList2);
        alarmTrendDiagramList.add(alarmTrendDiagramMap2);

        alarmTrendDiagramMap3.put("label", "低危");
        alarmTrendDiagramMap3.put("type", "alarmQuantity"); //调用量
        alarmTrendDiagramMap3.put("result", alarmTrendList3);
        alarmTrendDiagramList.add(alarmTrendDiagramMap3);

        alarmTrendDiagramTendency.put("data", alarmTrendDiagramList);

        return alarmTrendDiagramTendency;
    }

    /**
     * 场景告警任务趋势(13个场景)
     *
     * @return {@link Object}
     */
    @Override
    public Object scenarioTaskAlarmTrend(String beginDate, String endDate) {
        Map<String, Object> scenarioAlarmTrendView = new HashMap<>();

        List<Map<String, Object>> scenarioAlarmTrendList = new ArrayList<>();

        Console.log("获取{} - {}范围内场景告警任务趋势....", beginDate, endDate);
        List<String> everyDayList = DateUtils.findEveryDay(beginDate, endDate);

        List<Map<String, Object>> scenarioAlarmList1 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList2 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList3 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList4 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList5 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList6 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList7 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList8 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList9 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList10 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList11 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList12 = new ArrayList<>();
        List<Map<String, Object>> scenarioAlarmList13 = new ArrayList<>();

        for (int i = 0; i < everyDayList.size(); i++) {
            Map<String, Object> scenarioAlarmMap1 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap2 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap3 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap4 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap5 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap6 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap7 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap8 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap9 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap10 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap11 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap12 = new HashMap<>();
            Map<String, Object> scenarioAlarmMap13 = new HashMap<>();

            String theSameDay = everyDayList.get(i);

            List<Map<String, Object>> maps = icAlarmdisposalService.scenarioAlarmAmountByData(theSameDay, DateUtils.dealDays(theSameDay, 1));
            Map<String, Integer> mergedMap = new HashMap<>();
            for (Map<String, Object> alarmAmountMap : maps) {
                Object detectionmodel = alarmAmountMap.get("detectionmodel") == null ? "" : alarmAmountMap.get("detectionmodel");
                Object modelnum = alarmAmountMap.get("modelnum") == null ? 0 : alarmAmountMap.get("modelnum");
                mergedMap.put(String.valueOf(detectionmodel), Integer.parseInt(modelnum.toString()));
            }
//            int requestcontentSum = mergedMap.get(Const.DICT_API_INTERFACEINVOLVESACCOUNTPASSWORD) == null ? 0 : mergedMap.get(Const.DICT_API_INTERFACEINVOLVESACCOUNTPASSWORD);
            int sensitiveSum = mergedMap.get(Const.DICT_API_SENSITIVE_DATA) == null ? 0 : mergedMap.get(Const.DICT_API_SENSITIVE_DATA);
            int unexpectedSum = mergedMap.get(Const.DICT_API_UNEXPECTED_CONTENTS) == null ? 0 : mergedMap.get(Const.DICT_API_UNEXPECTED_CONTENTS);
            int outdatacallSum = mergedMap.get(Const.DICT_API_BURSTS_ACQUIRE_LARGE_DATA) == null ? 0 : mergedMap.get(Const.DICT_API_BURSTS_ACQUIRE_LARGE_DATA);
            int apiAuthCheckSum = mergedMap.get(Const.DICT_API_INTERFACE_AUTHENTICATION) == null ? 0 : mergedMap.get(Const.DICT_API_INTERFACE_AUTHENTICATION);
            int apiComplianceAuditSum = mergedMap.get(Const.DICT_API_COMPLIANCEAUDIT) == null ? 0 : mergedMap.get(Const.DICT_API_COMPLIANCEAUDIT);


            int silentAccountSum = mergedMap.get(Const.DICT_API_SILENT_ACCOUNT_BURST_ACCESS) == null ? 0 : mergedMap.get(Const.DICT_API_SILENT_ACCOUNT_BURST_ACCESS);
            int apiUserCallSum = mergedMap.get(Const.DICT_API_USER_VISITS_ARE_HIGH) == null ? 0 : mergedMap.get(Const.DICT_API_USER_VISITS_ARE_HIGH);
            int invokingrecordSum = mergedMap.get(Const.DICT_API_VISITS_ARE_HIGH) == null ? 0 : mergedMap.get(Const.DICT_API_VISITS_ARE_HIGH);
            int infrequentlycallSum = mergedMap.get(Const.DICT_API_SUDDEN_ACCESS_LONG_NON_VISITED) == null ? 0 : mergedMap.get(Const.DICT_API_SUDDEN_ACCESS_LONG_NON_VISITED);
            int nonperiodcallSum = mergedMap.get(Const.DICT_API_VISITS_DURING_ABNORMAL) == null ? 0 : mergedMap.get(Const.DICT_API_VISITS_DURING_ABNORMAL);


            int errorCheckSum = mergedMap.get(Const.DICT_API_CALL_ERROR) == null ? 0 : mergedMap.get(Const.DICT_API_CALL_ERROR);
            int apiAttackDetectionSum = mergedMap.get(Const.DICT_API_ATTACK_DETECTION) == null ? 0 : mergedMap.get(Const.DICT_API_ATTACK_DETECTION);
            int unusedCheckSum = mergedMap.get(Const.DICT_API_NO_CALL_VOLUME) == null ? 0 : mergedMap.get(Const.DICT_API_NO_CALL_VOLUME);

//            int sensitiveSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_SENSITIVE_DATA);
//            int unexpectedSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_UNEXPECTED_CONTENTS);
//            int outdatacallSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_BURSTS_ACQUIRE_LARGE_DATA);
//            int apiAuthCheckSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_INTERFACE_AUTHENTICATION);
//            int apiComplianceAuditSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_COMPLIANCEAUDIT);
//
//            int silentAccountSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_SILENT_ACCOUNT_BURST_ACCESS);
//            int apiUserCallSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_USER_VISITS_ARE_HIGH);
//            int invokingrecordSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_VISITS_ARE_HIGH);
//            int infrequentlycallSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_SUDDEN_ACCESS_LONG_NON_VISITED);
//            int nonperiodcallSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_VISITS_DURING_ABNORMAL);
//            int unusedCheckSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_NO_CALL_VOLUME);
//            int errorCheckSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_CALL_ERROR);
//            int apiAttackDetectionSum = icAlarmdisposalService.scenarioAlarmAmount(theSameDay, DateUtils.dealDays(theSameDay, 1), Const.DICT_API_ATTACK_DETECTION);

            scenarioAlarmMap1.put("data", theSameDay);
            scenarioAlarmMap1.put("alarmQuantity", sensitiveSum);
            scenarioAlarmList1.add(scenarioAlarmMap1);

            scenarioAlarmMap2.put("data", theSameDay);
            scenarioAlarmMap2.put("alarmQuantity", unexpectedSum);
            scenarioAlarmList2.add(scenarioAlarmMap2);

            scenarioAlarmMap3.put("data", theSameDay);
            scenarioAlarmMap3.put("alarmQuantity", outdatacallSum);
            scenarioAlarmList3.add(scenarioAlarmMap3);

            scenarioAlarmMap4.put("data", theSameDay);
            scenarioAlarmMap4.put("alarmQuantity", apiAuthCheckSum);
            scenarioAlarmList4.add(scenarioAlarmMap4);
            scenarioAlarmMap5.put("data", theSameDay);
            scenarioAlarmMap5.put("alarmQuantity", apiComplianceAuditSum);
            scenarioAlarmList5.add(scenarioAlarmMap5);

            scenarioAlarmMap6.put("data", theSameDay);
            scenarioAlarmMap6.put("alarmQuantity", silentAccountSum);
            scenarioAlarmList6.add(scenarioAlarmMap6);

            scenarioAlarmMap7.put("data", theSameDay);
            scenarioAlarmMap7.put("alarmQuantity", apiUserCallSum);
            scenarioAlarmList7.add(scenarioAlarmMap7);

            scenarioAlarmMap8.put("data", theSameDay);
            scenarioAlarmMap8.put("alarmQuantity", invokingrecordSum);
            scenarioAlarmList8.add(scenarioAlarmMap8);

            scenarioAlarmMap9.put("data", theSameDay);
            scenarioAlarmMap9.put("alarmQuantity", infrequentlycallSum);
            scenarioAlarmList9.add(scenarioAlarmMap9);

            scenarioAlarmMap10.put("data", theSameDay);
            scenarioAlarmMap10.put("alarmQuantity", nonperiodcallSum);
            scenarioAlarmList10.add(scenarioAlarmMap10);

            scenarioAlarmMap11.put("data", theSameDay);
            scenarioAlarmMap11.put("alarmQuantity", unusedCheckSum);
            scenarioAlarmList11.add(scenarioAlarmMap11);

            scenarioAlarmMap12.put("data", theSameDay);
            scenarioAlarmMap12.put("alarmQuantity", errorCheckSum);
            scenarioAlarmList12.add(scenarioAlarmMap12);

            scenarioAlarmMap13.put("data", theSameDay);
            scenarioAlarmMap13.put("alarmQuantity", apiAttackDetectionSum);
            scenarioAlarmList13.add(scenarioAlarmMap13);

        }

        Map<String, Object> alarmTrendDiagramMap1 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap2 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap3 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap4 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap5 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap6 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap7 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap8 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap9 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap10 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap11 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap12 = new HashMap<>();
        Map<String, Object> alarmTrendDiagramMap13 = new HashMap<>();

        alarmTrendDiagramMap1.put("label", "接口输出敏感内容");
        alarmTrendDiagramMap1.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap1.put("result", scenarioAlarmList1);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap1);

        alarmTrendDiagramMap2.put("label", "接口出现未预期内容");
        alarmTrendDiagramMap2.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap2.put("result", scenarioAlarmList2);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap2);

        alarmTrendDiagramMap3.put("label", "突发获取大量数据");
        alarmTrendDiagramMap3.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap3.put("result", scenarioAlarmList3);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap3);

        alarmTrendDiagramMap4.put("label", "接口鉴权");
        alarmTrendDiagramMap4.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap4.put("result", scenarioAlarmList4);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap4);

        alarmTrendDiagramMap5.put("label", "接口规范审计");
        alarmTrendDiagramMap5.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap5.put("result", scenarioAlarmList5);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap5);

        alarmTrendDiagramMap6.put("label", "静默账号突发访问API");
        alarmTrendDiagramMap6.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap6.put("result", scenarioAlarmList6);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap6);

        alarmTrendDiagramMap7.put("label", "用户API调用量");
        alarmTrendDiagramMap7.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap7.put("result", scenarioAlarmList7);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap7);

        alarmTrendDiagramMap8.put("label", "API调用量");
        alarmTrendDiagramMap8.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap8.put("result", scenarioAlarmList8);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap8);

        alarmTrendDiagramMap9.put("label", "不常调用API");
        alarmTrendDiagramMap9.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap9.put("result", scenarioAlarmList9);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap9);

        alarmTrendDiagramMap10.put("label", "非工作时段API调用");
        alarmTrendDiagramMap10.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap10.put("result", scenarioAlarmList10);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap10);

        alarmTrendDiagramMap11.put("label", "失活API");
        alarmTrendDiagramMap11.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap11.put("result", scenarioAlarmList11);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap11);

        alarmTrendDiagramMap12.put("label", "API调用错误");
        alarmTrendDiagramMap12.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap12.put("result", scenarioAlarmList12);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap12);

        alarmTrendDiagramMap13.put("label", "接口攻击");
        alarmTrendDiagramMap13.put("type", "alarmQuantity"); //告警量
        alarmTrendDiagramMap13.put("result", scenarioAlarmList13);
        scenarioAlarmTrendList.add(alarmTrendDiagramMap13);

        scenarioAlarmTrendView.put("data", scenarioAlarmTrendList);

        return scenarioAlarmTrendView;
    }

    /**
     * 接口告警气泡图
     *
     * @return {@link Object}
     */
    @Override
    public Object interfaceAlarmBubbleView(String beginDate, String endDate) {

        Map<String, Object> interfaceAlarmBubbleViewMap = new HashMap<>();

        List<Map<String, Object>> interfaceAlarmBubbleList = new ArrayList<>();

        Console.log("获取{} - {}范围内场景告警任务趋势....", beginDate, endDate);

        List<Map<String, Object>> interfaceAlarmMapList = icAlarmdisposalService.queryInterfaceAlarmsNum(beginDate, endDate);

        if (interfaceAlarmMapList.size() > 0) {
            for (Map<String, Object> stringObjectMap : interfaceAlarmMapList) {
                Map<String, Object> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("label", stringObjectMap.get("apicode"));
                objectObjectHashMap.put("value", stringObjectMap.get("num"));
                interfaceAlarmBubbleList.add(objectObjectHashMap);
            }
        }
        interfaceAlarmBubbleViewMap.put("result", interfaceAlarmBubbleList);
        return interfaceAlarmBubbleViewMap;
    }

}