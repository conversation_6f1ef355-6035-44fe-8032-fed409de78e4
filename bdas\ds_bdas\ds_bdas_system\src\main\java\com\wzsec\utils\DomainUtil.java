package com.wzsec.utils;

import cn.hutool.core.collection.CollUtil;
import com.wzsec.modules.ic.repository.IcApprovallogRepository;
import com.wzsec.modules.security.domain.JwtUserDto;
import com.wzsec.modules.system.service.UserService;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 分域接口编码查询工具类 - 供基于用户部门权限的接口编码查询功能(TODO 根据申请部门名称分域)
 *
 * <AUTHOR>
 * @date 2025/08/01
 */
@Slf4j
public class DomainUtil {

    /**
     * 查询当前用户分域接口编码
     *
     * @return 接口编码列表，如果查询失败或无权限则返回空列表
     */
    public static List<String> queryDomainApiCode() {
        try {
            JwtUserDto currentUser = getCurrentUser();
            if (currentUser == null) {
                log.warn("获取当前用户信息失败，返回空的接口编码列表");
                return Collections.singletonList("-1");
            }

            List<String> deptIds = currentUser.getDeptIds();
            //return queryDomainApiCodeByDeptIds(deptIds);
            return Arrays.asList("s_2746000000000_14377", "hnsylbzj_yljghybyshbmddjxx", "hnsylbzj_xyxfdjxx", "92wqAsPf8J32nE4e", "547b17fc84b18162");

        } catch (Exception e) {
            log.error("查询分域接口编码时发生异常", e);
            //return Collections.emptyList();
            return Arrays.asList("s_2746000000000_14377", "hnsylbzj_yljghybyshbmddjxx", "hnsylbzj_xyxfdjxx", "92wqAsPf8J32nE4e", "547b17fc84b18162");
        }
    }

    /**
     * 根据部门ID列表查询接口编码
     *
     * @param deptIds 部门ID列表
     * @return 接口编码列表，如果查询失败则返回空列表
     */
    public static List<String> queryDomainApiCodeByDeptIds(List<String> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            log.debug("部门ID列表为空，返回空的接口编码列表");
            return Collections.singletonList("-1");
        }

        try {
            List<String> apiCodeList = queryFromDatabase(deptIds);
            log.info("查询分域接口编码完成，部门IDs: {}, 接口数量: {}", deptIds, apiCodeList.size());
            return apiCodeList;

        } catch (Exception e) {
            log.error("根据部门ID查询接口编码时发生异常，部门IDs: {}", deptIds, e);
            return Collections.singletonList("-1");
        }
    }

    /**
     * 获取当前用户信息
     */
    private static JwtUserDto getCurrentUser() {
        try {
            UserService userService = SpringUtils.getBean(UserService.class);
            return userService.getJWTUserInfo();
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            return null;
        }
    }

    /**
     * 从数据库查询接口编码
     */
    private static List<String> queryFromDatabase(List<String> deptIds) {
        try {
            IcApprovallogRepository repository = SpringUtils.getBean(IcApprovallogRepository.class);
            List<String> result = repository.queryApiCodeByApplyorgName(deptIds);
            return result != null ? result : Collections.singletonList("-1");
        } catch (Exception e) {
            log.error("从数据库查询接口编码失败，部门IDs: {}", deptIds, e);
            return Collections.singletonList("-1");
        }
    }
}
