package com.wzsec.modules.ic.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.IcInvokingRecord;
import com.wzsec.modules.ic.service.IcInvokingRecordService;
import com.wzsec.modules.ic.service.dto.IcInvokingRecordQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2021-04-25
 */
// @Api(tags = "接口调用量管理")
@RestController
@RequestMapping("/api/icInvokingRecord")
public class IcInvokingRecordController {

    private final IcInvokingRecordService icInvokingRecordService;

    public IcInvokingRecordController(IcInvokingRecordService icInvokingRecordService) {
        this.icInvokingRecordService = icInvokingRecordService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, IcInvokingRecordQueryCriteria criteria) throws IOException {
        Timestamp dateStart = criteria.getDateStart();
        Timestamp dateEnd = criteria.getDateEnd();
        if (dateStart != null && dateEnd != null) {
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setInsertdatetime(checkTime);
        }

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        icInvokingRecordService.download(icInvokingRecordService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口调用量")
    public ResponseEntity<Object> getIcInvokingrecords(IcInvokingRecordQueryCriteria criteria, Pageable pageable) {
        Timestamp dateStart = criteria.getDateStart();
        Timestamp dateEnd = criteria.getDateEnd();
        if (dateStart != null && dateEnd != null) {
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setInsertdatetime(checkTime);
        }
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        return new ResponseEntity<>(icInvokingRecordService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口调用量")
    public ResponseEntity<Object> create(@Validated @RequestBody IcInvokingRecord resources) {
        return new ResponseEntity<>(icInvokingRecordService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口调用量")
    public ResponseEntity<Object> update(@Validated @RequestBody IcInvokingRecord resources) {
        icInvokingRecordService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口调用量")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        icInvokingRecordService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
