package com.wzsec.modules.ic.service;

import com.wzsec.modules.ic.domain.IcAlarmdisposal;
import com.wzsec.modules.ic.service.dto.IcAlarmdisposalDto;
import com.wzsec.modules.ic.service.dto.IcAlarmdisposalQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-10-09
 */
public interface IcAlarmdisposalService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(IcAlarmdisposalQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<IcAlarmdisposalDto>
     */
    List<IcAlarmdisposalDto> queryAll(IcAlarmdisposalQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return IcAlarmdisposalDto
     */
    IcAlarmdisposalDto findById(Integer id);

    /**
     * 创建
     *
     * @param resources /
     * @return IcAlarmdisposalDto
     */
    IcAlarmdisposalDto create(IcAlarmdisposal resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(IcAlarmdisposal resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Integer[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<IcAlarmdisposalDto> all, HttpServletResponse response) throws IOException;

    /**
     * 导出数据
     *
     * @param all 待导出的数据
     * @throws IOException /
     */
    String download(List<IcAlarmdisposalDto> all) throws IOException;


    /**
     * 导出数据邮件
     *
     * @param all 待导出的数据
     * @throws IOException /
     */
    String downloadMailExportExcel(List<IcAlarmdisposalDto> all) throws IOException;


    /**
     * 根据检测模型及接口编码查询入库数量
     *
     * @param apiCode        接口编码
     * @param detectionModel 检测模型
     * @return int
     */
    Integer queryQuantityByApiCodeAndModel(String apiCode, String detectionModel);

    /**
     * 合规审计风险修改
     *
     * @param risk           风险程度
     * @param apiCode        接口编码
     * @param detectionModel 检测模型
     */
    void complianceAuditRiskModification(String risk, String apiCode, String detectionModel);

    /**
     * 级别告警量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param risk      风险
     * @return int
     */
    Integer queryAlarmAmount(String beginDate, String endDate, String risk);


    /**
     * 场景告警量
     *
     * @param beginDate      开始日期
     * @param endDate        结束日期
     * @param detectionModel 检测模型
     * @return int
     */
    Integer scenarioAlarmAmount(String beginDate, String endDate, String detectionModel);

    /**
     * 场景告警量
     *
     * @param beginDate      开始日期
     * @param endDate        结束日期
     * @param detectionModel 检测模型
     * @return int
     */
    Integer scenarioAlarmAmount(String beginDate, String endDate, String detectionModel, List<String> domainApiCodeList);


    /**
     * 查询告警处置数量
     *
     * @param beginDate      开始日期
     * @param endDate        结束日期
     * @param treatmentState 处理状态
     * @return int
     */
    Integer queryAlarmDisposalNum(String beginDate, String endDate, String treatmentState);


    /**
     * 查询当日告警总量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    Integer queryTotalNumberAlarms(String beginDate, String endDate);


    /**
     * 获取告警累计数量
     *
     * @return int
     */
    Integer queryCumulativeDateInterface();

    /**
     * 查询风险程度告警数量
     *
     * @return int
     */
    Integer queryRiskAlarmAmount(String beginDate, String endDate, String risk);

    /**
     * 查询级别告警处置数量
     *
     * @param treatmentState 处理状态
     * @return int
     */
    Integer queryRiskAlarmDisposalNum(String beginDate, String endDate, String treatmentState);


    /**
     * 查询级别告警处置数量
     *
     * @param beginDate      开始日期
     * @param endDate        结束日期
     * @param treatmentState 处理状态
     * @return int
     */
    Integer queryRiskAlarmDisposalNum(String beginDate, String endDate, String treatmentState, String risk);

    /**
     * 查询级别告警处置数量
     *
     * @param treatmentState 处理状态
     * @return int
     */
    Integer queryRiskAlarmDisposalNum(String treatmentState);

    /**
     * 查询接口警报num
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     */
    List<Map<String, Object>> queryInterfaceAlarmsNum(String beginDate, String endDate);


    /**
     * 接口告警数量
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> queryInterfaceAlarmCounts();

    /**
     * 开始批量推送
     *
     * @param id id
     * @throws IOException /
     */
    void batchPush(Integer id);


    /**
     * 更新告警处置接口状态
     *
     * @return {@link String}
     */
    void updatealarmapistate(String eventId, String uuid);


    /**
     * 开始批量推送
     *
     * @param id id
     * @throws IOException /
     */
    void batchPush(String id, String risk, String treatmentstate, String note);


    /**
     * 接口告警数量
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> scenarioAlarmAmountByData(String beginDate, String endDate);

    /**
     * 接口告警数量
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> scenarioAlarmAmountByData(String beginDate, String endDate, List<String> domainApiCodeList);


    /**
     * 查询级别告警量
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> queryAlarmAmount(String beginDate, String endDate);

    /**
     * 查询级别告警量
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> queryAlarmAmount(String beginDate, String endDate, List<String> domainApiCodeList);

    /**
     * 查询级别告警量
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> queryRiskAlarm(String beginDate, String endDate, String risk);

    /**
     * 查询告警详情
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<String> queryingAlarmDetails(String beginDate, String endDate);

    /**
     * 查询告警详情
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<String> queryingAlarmDetails(String beginDate, String endDate, List<String> domainApiCodeList);

    /**
     * 查询告警处置数量
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> queryAlarmDisposalNum(String beginDate, String endDate);

    /**
     * 查询告警处置数量
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> queryAlarmDisposalNum(String beginDate, String endDate, List<String> domainApiCodeList);
}