package com.wzsec.modules.ic.service.dto;

import com.wzsec.annotation.Query;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-02-07
 */
@Data
public class IcConfigurationQueryCriteria {

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object apicode;

    @Query
    private String tasktype;

    @Query
    private String invoketype;

    @Query(blurry = "apicode,apiname")
    private String blurry;
}
