package com.wzsec.modules.ic.repository;

import com.wzsec.modules.ic.domain.IcInvokingRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-04-25
 */
public interface IcInvokingRecordRepository extends JpaRepository<IcInvokingRecord, Integer>, JpaSpecificationExecutor<IcInvokingRecord> {

    /**
     * 取随机接口
     *
     * @return {@link List}<{@link String}>
     */
    @Query(value = "SELECT DISTINCT apicode FROM sdd_ic_invokingrecord  ORDER BY RAND() LIMIT 5;  ", nativeQuery = true)
    List<String> takeRandomInterface();


    /**
     * 统计告警类型数量
     *
     * @return {@link Map}<{@link String}, {@link Long}>
     */
    @Query(value = "SELECT DATE(date) AS alarm_date, num AS occurrence_count FROM sdd_ic_invokingrecord WHERE date >= (CURRENT_DATE - INTERVAL 7 DAY) AND apicode = ?1 GROUP BY apicode,DATE(date) ORDER BY  alarm_date  ASC,  date ASC   ", nativeQuery = true)
    List<Map<String, Object>> queryAlarmVolumeByAlarmSource(String risk);

    /**
     * 查询七日平均统计告警 sparefield1 = 七日平均, sparefield2 = 当天调用量 / 七日平均 的值
     *
     * @param startDate
     * @param endDate
     * @return
     */
    //@Query(value = "SELECT *,a.num / a.sparefield1 AS sparefield2 FROM (SELECT t1.id,t1.apicode,t1.apimethod,t1.num,t1.date,t1.insertdatetime,t1.sparefield3,t1.sparefield4,(SELECT COALESCE( AVG( num ), 0 ) AS sparefield1 FROM sdd_ic_invokingrecord t3 \n" +
    //        "WHERE DATE_SUB( t1.date, INTERVAL 7 DAY ) <= date AND date < t1.date AND t3.apicode = t1.apicode ) AS sparefield1 FROM sdd_ic_invokingrecord t1,(SELECT apicode,date,MAX( insertdatetime ) maxinsertdatetime FROM sdd_ic_invokingrecord GROUP BY apicode,date ) t2 WHERE t1.apicode = t2.apicode AND t1.date = t2.date AND t1.insertdatetime = t2.maxinsertdatetime  AND t1.date >= ?1 AND t1.date <= ?2 ) a ORDER BY insertdatetime DESC,id DESC ", nativeQuery = true)
    //List<IcInvokingRecord> getInvokingRecordWarning(String startDate, String endDate);
    @Query(value = "SELECT * FROM sdd_ic_invokingrecord", nativeQuery = true)
    List<IcInvokingRecord> getInvokingRecordWarning(String startDate, String endDate);


    /**
     * 查询内容结果(word导出取五条)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link IcInvokingRecord}>
     */
    @Query(value = "SELECT * FROM sdd_ic_invokingrecord WHERE date BETWEEN :beginDate AND :endDate ORDER BY date, risk DESC LIMIT 5", nativeQuery = true)
    List<IcInvokingRecord> queryContentResult(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 查询调用量最大对象
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link IcInvokingRecord}
     */
    @Query(value = "SELECT * FROM sdd_ic_invokingrecord WHERE date BETWEEN :beginDate AND :endDate and risk > 0 ORDER BY num DESC LIMIT 1", nativeQuery = true)
    IcInvokingRecord maximumNumberOfCalls(@Param("beginDate") String beginDate, @Param("endDate") String endDate);


    /**
     * 查询调用量最大对象
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link IcInvokingRecord}
     */
    @Query(value = "SELECT * FROM sdd_ic_invokingrecord WHERE date BETWEEN :beginDate AND :endDate AND risk > 0 AND apicode IN (:domainApiCodeList)  ORDER BY num DESC LIMIT 1", nativeQuery = true)
    IcInvokingRecord maximumNumberOfCalls(@Param("beginDate") String beginDate, @Param("endDate") String endDate,@Param("domainApiCodeList") List<String> domainApiCodeList);

    /**
     * 日志调用总量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT SUM(num) FROM sdd_ic_invokingrecord WHERE date BETWEEN :beginDate AND :endDate  ", nativeQuery = true)
    int queryTotalLogs(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 日志调用总量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT SUM(num) FROM sdd_ic_invokingrecord WHERE date BETWEEN :beginDate AND :endDate AND apicode IN (:domainApiCodeList)  ", nativeQuery = true)
    int queryTotalLogs(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);

    /**
     * 接口编码调用量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT COUNT(DISTINCT apicode) FROM sdd_ic_invokingrecord WHERE date BETWEEN :beginDate AND :endDate ", nativeQuery = true)
    int queryTotalApiCode(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 接口编码调用量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT COUNT(DISTINCT apicode) FROM sdd_ic_invokingrecord WHERE date BETWEEN :beginDate AND :endDate AND apicode IN (:domainApiCodeList)", nativeQuery = true)
    int queryTotalApiCode(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);

    /**
     * 当天日志调用总量
     *
     * @return int
     */
    @Query(value = "SELECT SUM(num) FROM sdd_ic_invokingrecord WHERE insertdatetime BETWEEN :beginDate AND :endDate ", nativeQuery = true)
    BigInteger queryTotalLogsToDay(@Param("beginDate") String beginDate, @Param("endDate") String endDate);


    /**
     * 查询调用量接口
     *
     * @return {@link List}<{@link IcInvokingRecord}>
     */
    @Query(value = "SELECT *  FROM sdd_ic_invokingrecord WHERE TO_DAYS(NOW()) - TO_DAYS(insertdatetime) <= 0 AND sparefield4 IN(:taskNames) ORDER BY num DESC LIMIT 5", nativeQuery = true)
    List<IcInvokingRecord> queryTopTenCalls(@Param("taskNames") List<String> taskNameList);

    /**
     * 当日接口编码调用量
     *
     * @return int
     */
    @Query(value = "SELECT num FROM sdd_ic_invokingrecord WHERE date BETWEEN :beginDate AND :endDate AND apicode = :apicode LIMIT 1 ", nativeQuery = true)
    String queryInterfaceAmountToDay(@Param("apicode") String apicode, @Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 日志调用总量(检测时间)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT SUM(num) FROM sdd_ic_invokingrecord WHERE insertdatetime BETWEEN :beginDate AND :endDate  AND sparefield4 IN(:taskNames) ", nativeQuery = true)
    Integer queryTotalLogsByDetectionTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("taskNames") List<String> taskNameList);


    /**
     * 日志调用总量(检测时间)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT SUM(num) FROM sdd_ic_invokingrecord WHERE insertdatetime BETWEEN :beginDate AND :endDate ", nativeQuery = true)
    Integer queryTotalLogsByDetectionTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 接口编码调用量(检测时间)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT COUNT(DISTINCT apicode) FROM sdd_ic_invokingrecord WHERE insertdatetime BETWEEN :beginDate AND :endDate  AND  sparefield4 IN(:taskNames) ", nativeQuery = true)
    Integer queryTotalApiCodeByDetectionTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("taskNames") List<String> taskNameList);

    /**
     * 接口编码调用量(检测时间)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT COUNT(DISTINCT apicode) FROM sdd_ic_invokingrecord WHERE insertdatetime BETWEEN :beginDate AND :endDate  ", nativeQuery = true)
    Integer queryTotalApiCodeByDetectionTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate);


    /**
     * 接口编码调用量(检测时间)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT COUNT(DISTINCT apicode) FROM sdd_ic_invokingrecord WHERE insertdatetime BETWEEN :beginDate AND :endDate  AND sparefield4 IN(:taskNames) AND risk > 1 ", nativeQuery = true)
    Integer queryExceptionCall(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("taskNames") List<String> taskNameList);

    /**
     * 接口编码调用量(检测时间)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT COUNT(DISTINCT apicode) FROM sdd_ic_invokingrecord WHERE insertdatetime BETWEEN :beginDate AND :endDate  AND risk in ('2','3') ", nativeQuery = true)
    Integer queryExceptionCall(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 当日接口调用量(检测时间)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT COUNT(DISTINCT apicode) FROM sdd_ic_invokingrecord WHERE insertdatetime BETWEEN :beginDate AND :endDate ", nativeQuery = true)
    Integer queryCallToDay(@Param("beginDate") String beginDate, @Param("endDate") String endDate);


    /**
     * 查询7天平均值
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link String}>
     */
    @Query(value = "SELECT apicode FROM sdd_ic_invokingrecord WHERE date >= ?1 AND date <= ?2 GROUP BY apicode ORDER BY MAX(num) DESC LIMIT 5", nativeQuery = true)
    List<String> querySevenDayMeanValueTOP(String beginDate, String endDate);


    /**
     * 查询每日接口调用情况
     *
     * @param apicode apiCode
     * @return {@link IcInvokingRecord}
     */
    @Query(value = "SELECT num FROM sdd_ic_invokingrecord WHERE date = :theSameDay  AND apicode = :apicode LIMIT 1 ", nativeQuery = true)
    String queryDailyCallSituation(@Param("theSameDay") String theSameDay, @Param("apicode") String apicode);

    /**
     * 查询每日接口调用情况
     *
     * @return List<Map < String, Object>>
     */
    @Query(value = "SELECT CAST(date AS DATE) as date, COUNT(*) as num FROM sdd_ic_invokingrecord WHERE date BETWEEN :beginDate AND :endDate AND apicode = :apicode GROUP BY CAST(date AS DATE) ORDER BY CAST(date AS DATE)", nativeQuery = true)
    List<Map<String, Object>> queryDailyCall(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("apicode") String apicode);

}
