package com.wzsec.modules.ic.service;

import com.wzsec.modules.ic.domain.ApiOutdatacall;
import com.wzsec.modules.ic.service.dto.ApiOutdatacallDto;
import com.wzsec.modules.ic.service.dto.ApiOutdatacallQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
public interface ApiOutdatacallService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(ApiOutdatacallQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<ApiOutdatacallDto>
     */
    List<ApiOutdatacallDto> queryAll(ApiOutdatacallQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return ApiOutdatacallDto
     */
    ApiOutdatacallDto findById(Integer id);

    /**
     * 创建
     *
     * @param resources /
     * @return ApiOutdatacallDto
     */
    ApiOutdatacallDto create(ApiOutdatacall resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(ApiOutdatacall resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Integer[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<ApiOutdatacallDto> all, HttpServletResponse response) throws IOException;


    /**
     * 查询内容结果(word导出取五条)
     *
     * @param beginDate    开始日期
     * @param endDate      结束日期
     * @return {@link List}<{@link ApiOutdatacall}>
     */
    List<ApiOutdatacall> queryContentResult(String beginDate, String endDate);

    /**
     * 获取突发获取大量数据出现的接口数量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link Integer }
     */
    Integer apiOutDataCallApiCodeNum(String beginDate, String endDate);

    /**
     * 获取突发获取大量数据出现的接口数量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link Integer }
     */
    Integer apiOutDataCallApiCodeNum(String beginDate, String endDate, List<String> domainApiCodeList);

}