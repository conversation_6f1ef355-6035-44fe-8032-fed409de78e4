package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiSilentaccountcall;
import com.wzsec.modules.ic.service.ApiSilentaccountcallService;
import com.wzsec.modules.ic.service.dto.ApiSilentaccountcallQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
// @Api(tags = "静默账号突发访问API管理")
@RestController
@RequestMapping("/api/apiSilentaccountcall")
public class ApiSilentaccountcallController {

    private final ApiSilentaccountcallService apiSilentaccountcallService;

    public ApiSilentaccountcallController(ApiSilentaccountcallService apiSilentaccountcallService) {
        this.apiSilentaccountcallService = apiSilentaccountcallService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ApiSilentaccountcallQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        apiSilentaccountcallService.download(apiSilentaccountcallService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询静默账号突发访问API")
    public ResponseEntity<Object> getApiSilentaccountcalls(ApiSilentaccountcallQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        return new ResponseEntity<>(apiSilentaccountcallService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增静默账号突发访问API")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiSilentaccountcall resources) {
        return new ResponseEntity<>(apiSilentaccountcallService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改静默账号突发访问API")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiSilentaccountcall resources) {
        apiSilentaccountcallService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除静默账号突发访问API")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiSilentaccountcallService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
