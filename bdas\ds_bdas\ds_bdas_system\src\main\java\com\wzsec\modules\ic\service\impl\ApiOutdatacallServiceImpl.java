package com.wzsec.modules.ic.service.impl;

import com.wzsec.modules.ic.domain.ApiOutdatacall;
import com.wzsec.modules.ic.domain.IcInterfaceInfo;
import com.wzsec.modules.ic.domain.enums.RiskEnum;
import com.wzsec.modules.ic.repository.ApiOutdatacallRepository;
import com.wzsec.modules.ic.service.ApiOutdatacallService;
import com.wzsec.modules.ic.service.IcInterfaceInfoService;
import com.wzsec.modules.ic.service.dto.ApiOutdatacallDto;
import com.wzsec.modules.ic.service.dto.ApiOutdatacallQueryCriteria;
import com.wzsec.modules.ic.service.mapper.ApiOutdatacallMapper;
import com.wzsec.modules.system.service.DictDetailService;
import com.wzsec.utils.FileUtil;
import com.wzsec.utils.PageUtil;
import com.wzsec.utils.QueryHelp;
import com.wzsec.utils.ValidationUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
@Service
//@CacheConfig(cacheNames = "apiOutdatacall")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class ApiOutdatacallServiceImpl implements ApiOutdatacallService {

    private final ApiOutdatacallRepository apiOutdatacallRepository;

    private final ApiOutdatacallMapper apiOutdatacallMapper;

    private final IcInterfaceInfoService icInterfaceInfoService;

    private final DictDetailService dictDetailService;

    public ApiOutdatacallServiceImpl(ApiOutdatacallRepository apiOutdatacallRepository,
                                     ApiOutdatacallMapper apiOutdatacallMapper,
                                     IcInterfaceInfoService icInterfaceInfoService,
                                     DictDetailService dictDetailService) {
        this.apiOutdatacallRepository = apiOutdatacallRepository;
        this.apiOutdatacallMapper = apiOutdatacallMapper;
        this.icInterfaceInfoService = icInterfaceInfoService;
        this.dictDetailService = dictDetailService;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(ApiOutdatacallQueryCriteria criteria, Pageable pageable) {
        Map<String, IcInterfaceInfo> fullInterface = icInterfaceInfoService.findFullInterface();
        Page<ApiOutdatacall> page = apiOutdatacallRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(apiOutdatacallMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<ApiOutdatacallDto> queryAll(ApiOutdatacallQueryCriteria criteria) {
        Map<String, IcInterfaceInfo> fullInterface = icInterfaceInfoService.findFullInterface();
        List<ApiOutdatacallDto> dto = apiOutdatacallMapper.toDto(apiOutdatacallRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
        return dto;
    }

    @Override
    //@Cacheable(key = "#p0")
    public ApiOutdatacallDto findById(Integer id) {
        ApiOutdatacall apiOutdatacall = apiOutdatacallRepository.findById(id).orElseGet(ApiOutdatacall::new);
        ValidationUtil.isNull(apiOutdatacall.getId(), "ApiOutdatacall", "id", id);
        return apiOutdatacallMapper.toDto(apiOutdatacall);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public ApiOutdatacallDto create(ApiOutdatacall resources) {
        return apiOutdatacallMapper.toDto(apiOutdatacallRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(ApiOutdatacall resources) {
        ApiOutdatacall apiOutdatacall = apiOutdatacallRepository.findById(resources.getId()).orElseGet(ApiOutdatacall::new);
        ValidationUtil.isNull(apiOutdatacall.getId(), "ApiOutdatacall", "id", resources.getId());
        apiOutdatacall.copy(resources);
        apiOutdatacallRepository.save(apiOutdatacall);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            apiOutdatacallRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ApiOutdatacallDto> all, HttpServletResponse response) throws IOException {
        Map<String, String> systemNumberMap = dictDetailService.getDictDetailMap("system_number");
        List<Map<String, Object>> list = new ArrayList<>();
        Integer size = 0;
        for (ApiOutdatacallDto apiOutdatacall : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("调用方编码", apiOutdatacall.getAk());
            map.put("接口编码", apiOutdatacall.getApicode());
            map.put("接口名称", apiOutdatacall.getApiname());
            map.put("当日调用平均输出量", apiOutdatacall.getResultcount());
            map.put("七日调用平均输出量", apiOutdatacall.getAvgcount());
            map.put("风险程度", RiskEnum.getRisk(apiOutdatacall.getRisk()));
            map.put("系统标识", systemNumberMap.getOrDefault(apiOutdatacall.getSystemname(), ""));
            map.put("检测时间", apiOutdatacall.getChecktime());
            list.add(map);
            size = map.size();
        }
        FileUtil.downloadExcel(list, response, "突发获取大量数据检测结果", size);
    }

    @Override
    public List<ApiOutdatacall> queryContentResult(String beginDate, String endDate) {
        return apiOutdatacallRepository.queryContentResult(beginDate, endDate);
    }

    @Override
    public Integer apiOutDataCallApiCodeNum(String beginDate, String endDate) {
        return apiOutdatacallRepository.apiOutDataCallApiCodeNum(beginDate, endDate);
    }

    @Override
    public Integer apiOutDataCallApiCodeNum(String beginDate, String endDate, List<String> domainApiCodeList) {
        return apiOutdatacallRepository.apiOutDataCallApiCodeNum(beginDate, endDate, domainApiCodeList);
    }

}