package com.wzsec.modules.ic.repository;

import com.wzsec.modules.ic.domain.ApiOutdatacall;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
public interface ApiOutdatacallRepository extends JpaRepository<ApiOutdatacall, Integer>, JpaSpecificationExecutor<ApiOutdatacall> {

    /**
     * 查询内容结果(word导出取五条)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiOutdatacall}>
     */
    @Query(value = "SELECT * FROM sdd_api_outdatacall WHERE checktime BETWEEN :beginDate AND :endDate ORDER BY checktime, risk DESC ", nativeQuery = true)
    List<ApiOutdatacall> queryContentResult(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 获取突发获取大量数据出现的接口数量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiOutdatacall}>
     */
    @Query(value = "SELECT COUNT(DISTINCT apicode) FROM sdd_api_outdatacall WHERE checktime BETWEEN :beginDate AND :endDate  ", nativeQuery = true)
    Integer apiOutDataCallApiCodeNum(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 获取突发获取大量数据出现的接口数量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiOutdatacall}>
     */
    @Query(value = "SELECT COUNT(DISTINCT apicode) FROM sdd_api_outdatacall WHERE checktime BETWEEN :beginDate AND :endDate AND  apicode IN (:domainApiCodeList)", nativeQuery = true)
    Integer apiOutDataCallApiCodeNum(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);

}