package com.wzsec.modules.ic.repository;

import com.wzsec.modules.ic.domain.IcAlarmdisposal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-10-09
 */
public interface IcAlarmdisposalRepository extends JpaRepository<IcAlarmdisposal, Integer>, JpaSpecificationExecutor<IcAlarmdisposal> {

    /**
     * 取随机接口
     *
     * @return {@link List}<{@link String}>
     */
    @Query(value = "SELECT DISTINCT apicode FROM sdd_ic_alarmdisposal  ORDER BY RAND() LIMIT 5;  ", nativeQuery = true)
    List<String> takeRandomInterface();


    /**
     * 统计告警类型数量
     *
     * @return {@link Map}<{@link String}, {@link Long}>
     */
    @Query(value = "SELECT DATE(checktime) AS alarm_date, COUNT(*) AS occurrence_count FROM sdd_ic_alarmdisposal WHERE checktime >= (CURRENT_DATE - INTERVAL 7 DAY) AND risk=?1 GROUP BY risk,DATE(checktime) ORDER BY  alarm_date  ASC,  risk ASC  ", nativeQuery = true)
    List<Map<String, Object>> queryAlarmVolumeByAlarmSource(String risk);


    @Query(value = "SELECT COUNT(DISTINCT apicode)  FROM sdd_ic_alarmdisposal WHERE TO_DAYS(NOW()) - TO_DAYS(checktime) <= 0 AND risk != '1'", nativeQuery = true)
    int unqualifiedInterfaces();

    /**
     * 根据检测模型及接口编码查询入库数量
     *
     * @param apiCode        接口编码
     * @param detectionModel 检测模型
     * @return int
     */
    @Query(value = "SELECT COUNT(id)  FROM sdd_ic_alarmdisposal WHERE apicode=?1 AND detectionmodel = ?2", nativeQuery = true)
    int queryQuantityByApiCodeAndModel(String apiCode, String detectionModel);


    /**
     * 合规审计风险修改
     *
     * @param risk           风险程度
     * @param apiCode        接口编码
     * @param detectionModel 检测模型
     */
    @Modifying
    @Query(value = "update sdd_ic_alarmdisposal set risk = ?1 ,treatmentstate = 0  WHERE apicode=?2 AND detectionmodel = ?3", nativeQuery = true)
    void complianceAuditRiskModification(String risk, String apiCode, String detectionModel);


    /**
     * 查询级别告警量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param risk      风险
     * @return int
     */
    @Query(value = "SELECT COUNT(*)  FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  AND risk = :risk ", nativeQuery = true)
    int queryAlarmAmount(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("risk") String risk);


    /**
     * 查询场景告警量
     *
     * @param beginDate      开始日期
     * @param endDate        结束日期
     * @param detectionModel 检测模型
     * @return int
     */
    @Query(value = "SELECT COUNT(*)  FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  AND detectionmodel = :detectionModel ", nativeQuery = true)
    int scenarioAlarmAmount(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("detectionModel") String detectionModel);


    /**
     * 查询场景告警量
     *
     * @param beginDate      开始日期
     * @param endDate        结束日期
     * @param detectionModel 检测模型
     * @return int
     */
    @Query(value = "SELECT COUNT(*)  FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  AND detectionmodel = :detectionModel AND apicode IN (:domainApiCodeList) ", nativeQuery = true)
    int scenarioAlarmAmount(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("detectionModel") String detectionModel, @Param("domainApiCodeList") List<String> domainApiCodeList);


    /**
     * 查询告警处置数量
     *
     * @param beginDate      开始日期
     * @param endDate        结束日期
     * @param treatmentState 处理状态
     * @return int
     */
    @Query(value = "SELECT COUNT(*)  FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  AND treatmentstate = :treatmentState ", nativeQuery = true)
    int queryAlarmDisposalNum(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("treatmentState") String treatmentState);


    /**
     * 查询当日告警总量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT COUNT(*)  FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate", nativeQuery = true)
    int queryTotalNumberAlarms(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 获取告警累计数量
     *
     * @return int
     */
    @Query(value = "SELECT COUNT(*)  FROM sdd_ic_alarmdisposal ", nativeQuery = true)
    int queryCumulativeDateInterface();

    /**
     * 查询当日风险程度为高告警数量
     *
     * @return int
     */
    @Query(value = "SELECT COUNT(DISTINCT apicode)  FROM sdd_ic_alarmdisposal WHERE checktime  BETWEEN :beginDate AND :endDate  AND risk =:risk", nativeQuery = true)
    int queryRiskAlarmAmount(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("risk") String risk);


    /**
     * 查询当日场景告警量
     *
     * @param detectionModel 检测模型
     * @return int
     */
    @Query(value = "SELECT COUNT(*)  FROM sdd_ic_alarmdisposal WHERE TO_DAYS(NOW()) - TO_DAYS(checktime) <= 0  AND detectionmodel = :detectionModel  ", nativeQuery = true)
    int scenarioAlarmAmountToDay(@Param("detectionModel") String detectionModel);


    /**
     * 查询告警处置数量
     *
     * @param beginDate      开始日期
     * @param endDate        结束日期
     * @param treatmentState 处理状态
     * @param risk           风险
     * @return int
     */
    @Query(value = "SELECT COUNT(*)  FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  AND treatmentstate = :treatmentState AND risk = :risk ", nativeQuery = true)
    int queryRiskAlarmDisposalNum(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("treatmentState") String treatmentState, @Param("risk") String risk);


    /**
     * 查询告警处置数量
     *
     * @param beginDate      开始日期
     * @param endDate        结束日期
     * @param treatmentState 处理状态
     * @return int
     */
    @Query(value = "SELECT COUNT(*)  FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  AND treatmentstate = :treatmentState ", nativeQuery = true)
    int queryRiskAlarmDisposalNum(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("treatmentState") String treatmentState);


    /**
     * 查询告警处置数量
     *
     * @param treatmentState 处理状态
     * @return int
     */
    @Query(value = "SELECT COUNT(*)  FROM sdd_ic_alarmdisposal WHERE  treatmentstate = :treatmentState  ", nativeQuery = true)
    int queryRiskAlarmDisposalNum(@Param("treatmentState") String treatmentState);


    /**
     * 接口输出敏感内容数量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Query(value = "SELECT apicode, COUNT(apicode) AS num FROM sdd_ic_alarmdisposal  WHERE checktime BETWEEN :beginDate  AND :endDate   GROUP BY apicode ", nativeQuery = true)
    List<Map<String, Object>> queryInterfaceAlarmsNum(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    // /**
    //  * 接口告警数量
    //  *
    //  * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
    //  */
    // @Query(value = "SELECT apicode, COUNT(apicode) AS num FROM sdd_ic_alarmdisposal  WHERE  treatmentstate = '0'  GROUP BY apicode ORDER BY num DESC LIMIT 5", nativeQuery = true)
    // List<Map<String, Object>> queryInterfaceAlarmCounts();


    /**
     * 接口告警数量
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Query(value = "SELECT apicode, COUNT(*) AS num FROM sdd_ic_alarmdisposal GROUP BY apicode ORDER BY num DESC LIMIT 5", nativeQuery = true)
    List<Map<String, Object>> queryInterfaceAlarmCounts();

    /**
     * 查询推送数量
     *
     * @return int
     */
    @Query(value = "SELECT IFNULL(pushnumber,0) FROM sdd_ic_alarmdisposal WHERE id = ?1 ", nativeQuery = true)
    String queryPushTimes(Integer id);


    @Query(value = "SELECT COALESCE(CAST(pushnumber AS INTEGER), 0) FROM sdd_ic_alarmdisposal WHERE id = ?1", nativeQuery = true)
    String queryPushTimesForKingBase(Integer id);

    /**
     * 更新推送次数
     *
     * @param id
     * @param pushnumber
     */
    @Transactional
    @Modifying
    @Query(value = "update sdd_ic_alarmdisposal set pushnumber = ?2,reservefield5 = ?3,reservefield6 = ?4  where id = ?1", nativeQuery = true)
    void updatePushTimes(Integer id, Integer pushnumber, String username, String checktime);

    /**
     * 更新推送次数
     *
     * @param id
     * @param pushnumber
     */
    @Transactional
    @Modifying
    @Query(value = "update sdd_ic_alarmdisposal set pushnumber = ?2  where id = ?1", nativeQuery = true)
    void updatePushTimes(Integer id, Integer pushnumber);

    /**
     * 获取批量推送数据
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    @Query(value = "SELECT * FROM sdd_ic_alarmdisposal WHERE id = ?1", nativeQuery = true)
    IcAlarmdisposal obtainBatchPushData(Integer id);

    /**
     * 更新告警处置接口状态
     *
     * @return {@link List}<{@link String}>
     */
    @Transactional
    @Modifying
    @Query(value = "update sdd_ic_alarmdisposal set treatmentstate = ?2  where uuid = ?1 ", nativeQuery = true)
    void updatealarmapistate(String eventId, String state);


    /**
     * 更新推送次数
     *
     * @param id
     * @param pushnumber
     */
    @Transactional
    @Modifying
    @Query(value = "update sdd_ic_alarmdisposal set pushnumber = ?2,reservefield5 = ?3,reservefield6 = ?4,reservefield1 = ?5,treatmentstate = ?6,note = ?7  where id = ?1", nativeQuery = true)
    void updatePushTimes(Integer id, Integer pushnumber, String username, String checktime, String risk, String treatmentstate, String note);

    /**
     * 查询场景告警量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT detectionmodel,COUNT(*) as modelnum  FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  GROUP BY detectionmodel ", nativeQuery = true)
    List<Map<String, Object>> scenarioAlarmAmountByData(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 查询场景告警量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT detectionmodel,COUNT(*) as modelnum  FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  AND apicode IN (:domainApiCodeList)  GROUP BY detectionmodel ", nativeQuery = true)
    List<Map<String, Object>> scenarioAlarmAmountByData(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);


    /**
     * 查询级别告警量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT risk,COUNT(*) as reservefield6 FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  GROUP BY risk ", nativeQuery = true)
    List<Map<String, Object>> queryAlarmAmount(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 查询级别告警量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT risk,COUNT(*) as reservefield6 FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  AND apicode IN (:domainApiCodeList)  GROUP BY risk ", nativeQuery = true)
    List<Map<String, Object>> queryAlarmAmount(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);


    /**
     * 查询级别告警量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return List<Map < String, Object>>
     */
    @Query(value = "SELECT DATE(checktime) as checktime, COUNT(*) as reservefield6 FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate AND :endDate AND risk = :risk GROUP BY DATE(checktime) ORDER BY DATE(checktime)", nativeQuery = true)
    List<Map<String, Object>> queryRiskAlarm(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("risk") String risk);

    /**
     * 查询告警详情
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT checktime FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate", nativeQuery = true)
    List<String> queryingAlarmDetails(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 查询告警详情
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT checktime FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  AND apicode IN (:domainApiCodeList)", nativeQuery = true)
    List<String> queryingAlarmDetails(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);

    /**
     * 查询告警处置数量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT treatmentstate,COUNT(*) as reservefield6 FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  GROUP BY treatmentState ", nativeQuery = true)
    List<Map<String, Object>> queryAlarmDisposalNum(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 查询告警处置数量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    @Query(value = "SELECT treatmentstate,COUNT(*) as reservefield6 FROM sdd_ic_alarmdisposal WHERE checktime BETWEEN :beginDate  AND :endDate  AND apicode IN (:domainApiCodeList)  GROUP BY treatmentState ", nativeQuery = true)
    List<Map<String, Object>> queryAlarmDisposalNum(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);

    /**
     * 结果表查询
     *
     * @return {@link List}<{@link String}>
     */
    @Query(value = "SELECT detectionmodel,count(detectionmodel) as num FROM sdd_ic_alarmdisposal where apicode = ?1 AND checktime >= CURDATE() - INTERVAL 7 DAY AND checktime < CURDATE() group by detectionmodel", nativeQuery = true)
    List<Map<String, Object>> sceneNumberDistribution(String apicode);


    /**
     * 结果表查询
     *
     * @return {@link List}<{@link String}>
     */
    @Query(value = "SELECT risk,count(risk) as num FROM sdd_ic_alarmdisposal where apicode = ?1 AND checktime >= CURDATE() - INTERVAL 7 DAY AND checktime < CURDATE() group by risk", nativeQuery = true)
    List<Map<String, Object>> sensitivityLevelDistribution(String apicode);
}