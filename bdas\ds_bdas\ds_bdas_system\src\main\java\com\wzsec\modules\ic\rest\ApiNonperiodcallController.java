package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiNonperiodcall;
import com.wzsec.modules.ic.service.ApiNonperiodcallService;
import com.wzsec.modules.ic.service.dto.ApiNonperiodcallQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
// @Api(tags = "非工作时段API调用管理")
@RestController
@RequestMapping("/api/apiNonperiodcall")
public class ApiNonperiodcallController {

    private final ApiNonperiodcallService apiNonperiodcallService;

    public ApiNonperiodcallController(ApiNonperiodcallService apiNonperiodcallService) {
        this.apiNonperiodcallService = apiNonperiodcallService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ApiNonperiodcallQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        apiNonperiodcallService.download(apiNonperiodcallService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询非工作时段API调用")
    public ResponseEntity<Object> getApiNonperiodcalls(ApiNonperiodcallQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        return new ResponseEntity<>(apiNonperiodcallService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增非工作时段API调用")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiNonperiodcall resources) {
        return new ResponseEntity<>(apiNonperiodcallService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改非工作时段API调用")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiNonperiodcall resources) {
        apiNonperiodcallService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除非工作时段API调用")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiNonperiodcallService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
