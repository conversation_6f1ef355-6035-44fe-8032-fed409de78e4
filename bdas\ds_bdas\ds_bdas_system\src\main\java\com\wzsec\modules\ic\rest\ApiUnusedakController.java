package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiUnusedak;
import com.wzsec.modules.ic.service.ApiUnusedakService;
import com.wzsec.modules.ic.service.dto.ApiUnusedakQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-27
 */
// @Api(tags = "失活应用检测结果管理")
@RestController
@RequestMapping("/api/apiUnusedak")
public class ApiUnusedakController {

    private final ApiUnusedakService apiUnusedakService;

    public ApiUnusedakController(ApiUnusedakService apiUnusedakService) {
        this.apiUnusedakService = apiUnusedakService;
    }

    @Log("导出数据")
    public void download(HttpServletResponse response, ApiUnusedakQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        apiUnusedakService.download(apiUnusedakService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询失活应用检测结果")
    public ResponseEntity<Object> getApiUnusedaks(ApiUnusedakQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        return new ResponseEntity<>(apiUnusedakService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增失活应用检测结果")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiUnusedak resources) {
        return new ResponseEntity<>(apiUnusedakService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改失活应用检测结果")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiUnusedak resources) {
        apiUnusedakService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除失活应用检测结果")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiUnusedakService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
