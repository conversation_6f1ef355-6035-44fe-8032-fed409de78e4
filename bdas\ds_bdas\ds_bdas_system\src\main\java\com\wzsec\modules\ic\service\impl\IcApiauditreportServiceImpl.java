package com.wzsec.modules.ic.service.impl;

import cn.afterturn.easypoi.entity.ImageEntity;
import com.wzsec.modules.ic.domain.*;
import com.wzsec.modules.ic.domain.enums.RiskEnum;
import com.wzsec.modules.ic.service.*;
import com.wzsec.modules.ic.service.dto.IcApiauditreportQueryCriteria;
import com.wzsec.utils.Const;
import com.wzsec.utils.DateUtils;
import com.wzsec.utils.FileUtil;
import com.wzsec.utils.JfreeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jfree.data.time.Day;
import org.jfree.data.time.TimeSeries;
import org.jfree.data.time.TimeSeriesCollection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 审计报告导出(TODO 分域结果集)
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@Service
@Slf4j
public class IcApiauditreportServiceImpl implements IcApiauditreportService {

    @Autowired
    private IcInterfaceInfoService icInterfaceInfoService;

    @Autowired
    private IcAlarmdisposalService icAlarmdisposalService;

    @Autowired
    private IcResultDetailService icResultDetailService;

    @Autowired
    private ApiUnexpectedcontentresultService apiUnexpectedcontentresultService;

    @Autowired
    private ApiOutdatacallService apiOutdatacallService;

    @Autowired
    private ApiAuthcheckresultService apiAuthcheckresultService;

    @Autowired
    private ApiComplianceauditService apiComplianceauditService;

    @Autowired
    private ApiSilentaccountcallService apiSilentaccountcallService;

    @Autowired
    private ApiUsercallService apiUsercallService;

    @Autowired
    private IcInvokingRecordService icInvokingRecordService;

    @Autowired
    private ApiInfrequentlycallService apiInfrequentlycallService;

    @Autowired
    private ApiNonperiodcallService apiNonperiodcallService;

    @Autowired
    private ApiUnusedcheckresultService apiUnusedcheckresultService;

    @Autowired
    private ApiErrorcheckresultService apiErrorcheckresultService;

    @Autowired
    private ApiAttackdetectionService apiAttackdetectionService;

    @Autowired
    private ApiInvolvesaccountpasswordService apiInvolvesaccountpasswordService;

    @Autowired
    private ApiUnusedakService apiUnusedakService;

    private static final int TOP_N = 5;

    @Override
    public String downloadApiAuditReport(IcApiauditreportQueryCriteria criteria,
                                         HttpServletResponse response,
                                         List<String> domainApiCodeList) throws IOException {
        //时间取值,默认导出一周结果
        String beginDate = "";
        String endDate = "";
        String exportDate = DateUtils.getDate();
        if (criteria.getDateStart() != null && criteria.getDateEnd() != null) {
            beginDate = new SimpleDateFormat("yyyy-MM-dd").format(criteria.getDateStart());
            endDate = new SimpleDateFormat("yyyy-MM-dd").format(criteria.getDateEnd());
        } else {
            beginDate = DateUtils.dealDays(DateUtils.getDate(), -7);
            endDate = DateUtils.dealDays(DateUtils.getDate(), 1);
        }
        // 获取map,缓存用户信息,避免重复查库
        List<Map<String, String>> maps1 = icInterfaceInfoService.queryApicodeMap();
        Map<String, String> icInterfaceInfoMap = new HashMap<>();
        for (Map<String, String> stringStringMap : maps1) {
            String apicode = stringStringMap.get("apicode") == null ? "" : stringStringMap.get("apicode");
            String apiname = stringStringMap.get("apiname") == null ? "" : stringStringMap.get("apiname");
            icInterfaceInfoMap.put(apicode, apiname);
        }

        // API审计报告导出时间
        String export = DateUtils.getDate("yyyy-MM-dd HH:mm:ss");
        log.info("【{}开始导出{}至{} API审计报告】", export, beginDate, exportDate);


        HashMap<String, Object> map = new HashMap<>();
        map.put("beginDate", beginDate); //周期开始时间
        map.put("endDate", endDate); //周期结束时间

        beginDate = beginDate + " 00:00:00";
        endDate = endDate + " 23:59:59";

        // TODO 1. 接口请求内容检测
        interfaceRequestContentDetection(beginDate, endDate, map, domainApiCodeList);
        // TODO 2. 接口输出敏感数据检测
        interfaceOutputSensitiveContentDetection(beginDate, endDate, map, domainApiCodeList);
        // TODO 3.接口出现未预期内容
        unexpectedContentInTheInterface(beginDate, endDate, map, icInterfaceInfoMap, domainApiCodeList);
        // TODO 4.突发获取大量数据
        suddenAcquisitionOfLargeAmountsOfData(beginDate, endDate, icInterfaceInfoMap, map, domainApiCodeList);
        // TODO 5.接口鉴权检测
        interfaceAuthenticationDetection(beginDate, endDate, map, domainApiCodeList);
        // TODO 6.接口规范审计
        interfaceSpecificationAudit(beginDate, endDate, map, domainApiCodeList);
        // TODO 7.静默账号突发访问API
        silentAccountSuddenAccessAPI(beginDate, endDate, map, domainApiCodeList);
        // TODO 8.用户API调用量
        userAPICallVolume(beginDate, endDate, icInterfaceInfoMap, map, domainApiCodeList);
        // TODO 9.API调用量
        apiCallVolume(beginDate, endDate, icInterfaceInfoMap, map, domainApiCodeList);
        // TODO 10.不常调用API
        notFrequentlyCallingAPIs(beginDate, endDate, icInterfaceInfoMap, map, domainApiCodeList);
        // TODO 11.非工作时段调用
        callDuringNonWorkingHours(beginDate, endDate, icInterfaceInfoMap, map, domainApiCodeList);
        // TODO 12.失活API
        deactivationAPIDetection(beginDate, endDate, icInterfaceInfoMap, map, domainApiCodeList);
        // TODO 13.失活应用
        deactiveApplicationDetection(beginDate, endDate, map, domainApiCodeList);
        // TODO 13.API调用错误检测
        apiCallErrorDetection(beginDate, endDate, icInterfaceInfoMap, map, domainApiCodeList);
        // TODO 14.接口攻击检测
        interfaceAttackDetection(beginDate, endDate, icInterfaceInfoMap, map, domainApiCodeList);
        // TODO 告警概要及图形
        alarmSummaryAndGraphics(beginDate, endDate, map, domainApiCodeList);

        //word模板相对路径、word生成路径、word生成的文件名称、数据源
        String tempPath = "";
        if (criteria.isExportNot()) {
            //通过mail发送审计报告
            tempPath = FileUtil.mailExportWord("API审计报告模板.docx", map);
        } else {
            FileUtil.exportWord("API审计报告模板.docx", map, response);
        }
        return tempPath;
    }

    /**
     * 告警概要及图形展示
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param map       地图
     */
    private void alarmSummaryAndGraphics(String beginDate, String endDate, HashMap<String, Object> map, List<String> domainApiCodeList) {

        // 场景告警数量查询
        // 分域结果集
        List<Map<String, Object>> maps = icAlarmdisposalService.scenarioAlarmAmountByData(beginDate, endDate, domainApiCodeList);
        Map<String, Integer> mergedMap = new HashMap<>();
        for (Map<String, Object> alarmAmountMap : maps) {
            Object detectionmodel = alarmAmountMap.get("detectionmodel") == null ? "" : alarmAmountMap.get("detectionmodel");
            Object modelnum = alarmAmountMap.get("modelnum") == null ? 0 : alarmAmountMap.get("modelnum");
            mergedMap.put(String.valueOf(detectionmodel), Integer.parseInt(modelnum.toString()));
        }

        int unusedCheckSum = mergedMap.getOrDefault(Const.DICT_API_NO_CALL_VOLUME, 0);
        int apiUnusedAk = mergedMap.getOrDefault(Const.INTERFACE_NO_CALL_AK, 0);

        // 合并场景告警数量
        int requestcontentSum = mergedMap.getOrDefault(Const.DICT_API_INTERFACEINVOLVESACCOUNTPASSWORD, 0);
        int sensitiveSum = mergedMap.getOrDefault(Const.DICT_API_SENSITIVE_DATA, 0);
        int unexpectedSum = mergedMap.getOrDefault(Const.DICT_API_UNEXPECTED_CONTENTS, 0);
        int outdatacallSum = mergedMap.getOrDefault(Const.DICT_API_BURSTS_ACQUIRE_LARGE_DATA, 0);
        int apiAuthCheckSum = mergedMap.getOrDefault(Const.DICT_API_INTERFACE_AUTHENTICATION, 0);
        int apiComplianceAuditSum = mergedMap.getOrDefault(Const.DICT_API_COMPLIANCEAUDIT, 0);
        int silentAccountSum = mergedMap.getOrDefault(Const.DICT_API_SILENT_ACCOUNT_BURST_ACCESS, 0);
        int apiUserCallSum = mergedMap.getOrDefault(Const.DICT_API_USER_VISITS_ARE_HIGH, 0);
        int invokingrecordSum = mergedMap.getOrDefault(Const.DICT_API_VISITS_ARE_HIGH, 0);
        int infrequentlycallSum = mergedMap.getOrDefault(Const.DICT_API_SUDDEN_ACCESS_LONG_NON_VISITED, 0);
        int nonperiodcallSum = mergedMap.getOrDefault(Const.DICT_API_VISITS_DURING_ABNORMAL, 0);
        int errorCheckSum = mergedMap.getOrDefault(Const.DICT_API_CALL_ERROR, 0);
        int apiAttackDetectionSum = mergedMap.getOrDefault(Const.DICT_API_ATTACK_DETECTION, 0);

        int apiContainsSensitive = mergedMap.getOrDefault(Const.DICT_API_CONTAINS_SENSITIVE, 0); //TODO 接口涉敏检测
        int apiRealTimeCall = mergedMap.getOrDefault(Const.DICT_API_REAL_TIME_CALL, 0); // TODO 接口实时调用检测


        // 计算告警总量
        int sumRisk = requestcontentSum + sensitiveSum + unexpectedSum + outdatacallSum +
                apiAuthCheckSum + apiComplianceAuditSum + silentAccountSum + apiUserCallSum +
                invokingrecordSum + infrequentlycallSum + nonperiodcallSum + unusedCheckSum +
                apiUnusedAk + errorCheckSum + apiAttackDetectionSum + apiContainsSensitive + apiRealTimeCall;

        // 查询告警数量
        // 分域结果集
        List<Map<String, Object>> alarmAmountMaps = icAlarmdisposalService.queryAlarmAmount(beginDate, endDate, domainApiCodeList);
        Map<String, Integer> alarmMaps = new HashMap<>();
        for (Map<String, Object> alarmAmountMap : alarmAmountMaps) {
            Object risk = alarmAmountMap.get("risk") == null ? "" : alarmAmountMap.get("risk");
            Object reservefield6 = alarmAmountMap.get("reservefield6") == null ? "0" : alarmAmountMap.get("reservefield6");
            alarmMaps.put(String.valueOf(risk), Integer.parseInt(reservefield6.toString()));
        }

        // 分类告警数量
        int hightRiskNum = alarmMaps.getOrDefault(Const.RISK_HIGH, 0);
        int middleNum = alarmMaps.getOrDefault(Const.RISK_MIDDLE, 0);
        int lowNum = alarmMaps.getOrDefault(Const.RISK_LOW, 0);

        // 填充告警明细表
        map.put("requestcontentSum", requestcontentSum); // 接口请求内容告警数量
        map.put("sensitiveSum", sensitiveSum); // 接口输出敏感数据告警数量
        map.put("unexpectedSum", unexpectedSum); // 接口出现未预期内容告警数量
        map.put("outdatacallSum", outdatacallSum); // 突发获取大量数据告警数量
        map.put("apiAuthCheckSum", apiAuthCheckSum); // 接口鉴权告警数量
        map.put("apiComplianceAuditSum", apiComplianceAuditSum); // 接口合规审计告警数量
        map.put("silentAccountSum", silentAccountSum); // 静默账号突发访问API告警数量
        map.put("apiUserCallSum", apiUserCallSum); // 用户API调用量告警数量
        map.put("invokingrecordSum", invokingrecordSum); // API调用量告警数量
        map.put("infrequentlycallSum", infrequentlycallSum); // 不常调用API告警数量
        map.put("nonperiodcallSum", nonperiodcallSum); // 非工作时段调用API告警数量
        map.put("unusedCheckSum", unusedCheckSum); // 失活API告警数量
        map.put("apiUnusedAkSum", apiUnusedAk); // 失活应用告警数量
        map.put("errorCheckSum", errorCheckSum); // API调用错误告警数量
        map.put("apiAttackDetectionSum", apiAttackDetectionSum); // 接口攻击告警数量
        map.put("apiContainsSensitive", apiContainsSensitive); //TODO 接口涉敏检测
        map.put("apiRealTimeCall", apiRealTimeCall); // TODO 接口实时调用检测
        map.put("alarmSum", sumRisk); // 告警总量

        // API检测模型告警数量分部图
        HashMap<String, Integer> modelData = new HashMap<>();
        modelData.put(Const.API_OUTPUT_SENSITIVE_CONTENT, sensitiveSum); // 接口输出敏感数据
        modelData.put(Const.API_UNEXPECTED_CONTENT, unexpectedSum); // 接口出现未预期内容
        modelData.put(Const.API_BURST_ACCESS_LARGE_AMOUNTS_DATA, outdatacallSum); // 突发获取大量数据
        modelData.put(Const.API_AUTHENTICATION, apiAuthCheckSum); // 接口鉴权
        modelData.put(Const.API_COMPLIANCE_AUDIT, apiComplianceAuditSum); // 接口合规审计
        modelData.put(Const.API_SILENT_ACCOUNT_SUDDEN_ACCESS, silentAccountSum); // 静默账号突发访问API
        modelData.put(Const.API_USER_ADJUSTMENT_AMOUNT, apiUserCallSum); // 用户API调用量
        modelData.put(Const.API_CALLS_NUMBER, invokingrecordSum); // API调用量
        modelData.put(Const.API_INVOKED_INFREQUENTLY, infrequentlycallSum); // 不常调用API
        modelData.put(Const.API_NONWORKING_CALL, nonperiodcallSum); // 非工作时段API调用
        modelData.put(Const.API_INACTIVATION, unusedCheckSum); // 失活API
        modelData.put(Const.API_DEACTIVATED_APPLICATIONS, apiUnusedAk); // 失活API
        modelData.put(Const.API_CALL_ERROR, errorCheckSum); // API调用错误
        modelData.put(Const.API_ATTACK, apiAttackDetectionSum); // 接口攻击
        modelData.put(Const.API_CONTAINS_SENSITIVE, apiContainsSensitive); // 接口涉敏检测
        modelData.put(Const.API_REAL_TIME_CALL, apiRealTimeCall); // 接口实时调用检测

        String alarmMax = findMapMaxKey(modelData);
        Integer alarmNum = modelData.get(alarmMax);

        // 生成API检测模型数量告警分布图
        ImageEntity modelImageEntity = JfreeUtils.pieChart("API检测模型数量告警分布图", modelData, 500, 300, null);
        map.put("alarmDistributionPicture", modelImageEntity);

        // API告警等级分布图
        HashMap<String, Integer> riskData = new HashMap<>(3);
        riskData.put(Const.SEVERITY_HIGH, hightRiskNum);
        riskData.put(Const.SEVERITY_MIDDLE, middleNum);
        riskData.put(Const.SEVERITY_LOW, lowNum);
        // 饼状图颜色定义
        Map<String, Color> legendColorMap = new HashMap<>();
        legendColorMap.put(Const.SEVERITY_HIGH, Color.RED);
        legendColorMap.put(Const.SEVERITY_MIDDLE, Color.ORANGE);
        legendColorMap.put(Const.SEVERITY_LOW, Color.cyan);
        ImageEntity riskImageEntity = JfreeUtils.pieChart("API告警等级分布图", riskData, 500, 300, legendColorMap);
        map.put("alarmRiskPicture", riskImageEntity);

        // 告警趋势图
        TimeSeriesCollection lineDataset = new TimeSeriesCollection();
        TimeSeries timeSeries = new TimeSeries("当日告警数量");
        List<String> everyDay = DateUtils.findEveryDay(beginDate, endDate);

        // 分域结果集
        List<String> icAlarmdisposalList = icAlarmdisposalService.queryingAlarmDetails(beginDate, endDate, domainApiCodeList);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (String sameDay : everyDay) {
            String[] split = sameDay.split("-");
            // 获取场景当日告警总量
            String dayFollow = DateUtils.dealDays(sameDay, 1);
            LocalDateTime start = LocalDateTime.parse(sameDay + " 00:00:00", formatter);
            LocalDateTime end = LocalDateTime.parse(dayFollow + " 00:00:00", formatter);
            List<String> collect = icAlarmdisposalList.stream()
                    .filter(time -> LocalDateTime.parse(time, formatter).isAfter(start) && LocalDateTime.parse(time, formatter).isBefore(end))
                    .collect(Collectors.toList());

            timeSeries.add(new Day(Integer.parseInt(split[2]), Integer.parseInt(split[1]),
                    Integer.parseInt(split[0])), collect.size());
        }
        lineDataset.addSeries(timeSeries);
        ImageEntity alarmTrendPicture = JfreeUtils.lineChart("告警趋势图", "日期", "告警量",
                500, 300, lineDataset);
        map.put("alarmTrendPicture", alarmTrendPicture);

        // 查询告警处置数量
        // 分域结果集
        List<Map<String, Object>> maps2 = icAlarmdisposalService.queryAlarmDisposalNum(beginDate, endDate, domainApiCodeList);
        Map<String, Integer> alarmDisposalNumMaps = new HashMap<>();
        for (Map<String, Object> alarmAmountMap : maps2) {
            Object treatmentstate = alarmAmountMap.get("treatmentstate") == null ? "" : alarmAmountMap.get("treatmentstate");
            Object reservefield6 = alarmAmountMap.get("reservefield6") == null ? 0 : alarmAmountMap.get("reservefield6");
            alarmDisposalNumMaps.put(String.valueOf(treatmentstate), Integer.parseInt(reservefield6.toString()));
        }

        int disposeNum = alarmDisposalNumMaps.getOrDefault(Const.INTERFACE_ALARM_DISPOSAL_PROCESSED, 0);
        int noDisposeNum = alarmDisposalNumMaps.getOrDefault(Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED, 0);
        int overlookNum = alarmDisposalNumMaps.getOrDefault(Const.INTERFACE_ALARM_DISPOSAL_IGNORED, 0);

        // 填充告警概要
        map.put("disposeNum", disposeNum); // 已处置告警
        map.put("noDisposeNum", noDisposeNum); // 未处置告警
        map.put("overlookNum", overlookNum); // 已忽略告警
        map.put("alarmClassification", 3); // 告警分级 默认敏感级别为3
        map.put("hightRiskNum", hightRiskNum); // 高危告警数量
        map.put("middleNum", middleNum); // 中危告警数量
        map.put("lowNum", lowNum); // 低危告警数量
        map.put("alarmCategory", 16); // 告警类别数量 默认场景为14
        map.put("alarmMax", alarmMax); // 数量最多告警类别
        map.put("alarmNum", alarmNum); // 告警类别最多告警数量
    }

    /**
     * 失活应用
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param map       地图
     * @return int
     */
    private int deactiveApplicationDetection(String beginDate, String endDate, HashMap<String, Object> map, List<String> domainApiCodeList) {
        Set<String> recentlyDeactivatedApiSet = new HashSet<>();
        String apiUnusedCheckResultApiCode = "";
        int queryQuantity = 0;

        // 分域结果集
        String inactivationLatestTime = apiUnusedakService.queryLatestTime(beginDate, endDate, domainApiCodeList);

        if (StringUtils.isNotBlank(inactivationLatestTime)) {
            String inactivationOneDayLate = DateUtils.dealDays(inactivationLatestTime, 1);
            List<ApiUnusedak> apiUnusedCheckResults = apiUnusedakService.queryLatestUnusedCheckResult(inactivationLatestTime.substring(0, 10), inactivationOneDayLate);

            // 分域结果集
            apiUnusedCheckResults = apiUnusedCheckResults.stream()
                    .filter(item -> domainApiCodeList.contains(item.getApicode()))
                    .collect(Collectors.toList());

            // 分域结果集
            queryQuantity = apiUnusedakService.queryQuantity(inactivationLatestTime.substring(0, 10), inactivationOneDayLate, domainApiCodeList);

            if (!apiUnusedCheckResults.isEmpty()) {
                for (int i = 0; i < Math.min(apiUnusedCheckResults.size(), 5); i++) {
                    String apiUnusedAk = apiUnusedCheckResults.get(i).getAk(); //AK
                    String apiUnusedAkIp = apiUnusedCheckResults.get(i).getReqip(); //请求IP
                    String apiUnusedCheckUri = apiUnusedCheckResults.get(i).getApiurl(); //接口URI
                    String apiUnusedCheckApiCode = apiUnusedCheckResults.get(i).getApicode(); //接口编码
                    String apiUnusedCheckApiName = apiUnusedCheckResults.get(i).getApiname(); //接口名称

                    map.put("apiUnusedAk" + i, apiUnusedAk);
                    map.put("apiUnusedAkIp" + i, apiUnusedAkIp);
                    map.put("apiUnusedAkUri" + i, apiUnusedCheckUri);
                    map.put("apiUnusedAkApiCode" + i, apiUnusedCheckApiCode);
                    map.put("apiUnusedAkApiName" + i, apiUnusedCheckApiName);

                    recentlyDeactivatedApiSet.add(apiUnusedCheckApiCode);
                }

                for (int i = apiUnusedCheckResults.size(); i < 5; i++) {
                    map.put("apiUnusedAk" + i, "");
                    map.put("apiUnusedAkIp" + i, "");
                    map.put("apiUnusedAkUri" + i, "");
                    map.put("apiUnusedAkApiCode" + i, "");
                    map.put("apiUnusedAkApiName" + i, "");
                }
            }

            apiUnusedCheckResultApiCode = StringUtils.join(recentlyDeactivatedApiSet, ",");
        }

        map.put("apiUnusedAkNum", queryQuantity); //失活应用数量
        map.put("apiUnusedAk", apiUnusedCheckResultApiCode); //失活应用拼接
        return queryQuantity;
    }


    /**
     * 接口输出敏感数据检测
     *
     * @param beginDate          开始日期
     * @param endDate            结束日期
     * @param map                地图
     */
    private void interfaceOutputSensitiveContentDetection(String beginDate, String endDate, HashMap<String, Object> map, List<String> domainApiCodeList) {
        Map<String, Integer> sensitiveApiCodeNumMap = new HashMap<>();

        List<Map<String, Object>> sensitiveApiCodeList = icResultDetailService.sensitiveApiCodeNum(beginDate, endDate);
        String sensitiveMaxApiCode = "";
        int sensitiveApiCodeNum = 0;
        int sensitiveMaxNum = 0;

        if (!sensitiveApiCodeList.isEmpty()) {
            for (Map<String, Object> mapEntry : sensitiveApiCodeList) {
                String apiCode = (String) mapEntry.get("apicode");
                int num = ((BigDecimal) mapEntry.get("num")).intValue();
                // 分域结果集
                if (domainApiCodeList.contains(apiCode)) {
                    sensitiveApiCodeNumMap.put(apiCode, num);
                }
            }

            sensitiveMaxApiCode = findMapMaxKey(sensitiveApiCodeNumMap);
            sensitiveApiCodeNum = sensitiveApiCodeNumMap.size();
            sensitiveMaxNum = sensitiveApiCodeNumMap.getOrDefault(sensitiveMaxApiCode, 0);
        }

        int interfaceNum = 0;
        try {
            // 分域结果集
            interfaceNum = icInvokingRecordService.queryTotalApiCode(beginDate, endDate, domainApiCodeList);
        } catch (Exception ignored) {
        }

        map.put("apiCodeSum", interfaceNum); //监测接口个数
        map.put("sensitiveApiCodeNum", sensitiveApiCodeNum); //涉及输出敏感数据接口数量

        StringBuilder interfaceOutputSensitive = new StringBuilder();
        List<String[]> sensitiveList = icResultDetailService.queryDescriptionInterfaceSensitiveType(beginDate, endDate);

        // 分域结果集
        sensitiveList = sensitiveList.stream()
                .filter(row -> domainApiCodeList.contains(row[2]))
                .collect(Collectors.toList());

        for (int i = 0; i < 5; i++) {
            if (i < sensitiveList.size()) {
                String[] sensitiveData = sensitiveList.get(i);
                map.put("sensitiveIp" + i, sensitiveData[0]);
                map.put("sensitiveUri" + i, sensitiveData[1]);
                map.put("sensitiveApiCode" + i, sensitiveData[2]);
                map.put("sensitiveApiName" + i, sensitiveData[3]);
                map.put("outputSensitiveType" + i, sensitiveData[4]);
                map.put("sensitiveRisk" + i, RiskEnum.getRisk(sensitiveData[5]));
                map.put("sensitiveAk" + i, sensitiveData[6]);

                interfaceOutputSensitive.append(sensitiveData[2]).append("(").append(sensitiveData[4]).append("),");
            } else {
                map.put("sensitiveIp" + i, "");
                map.put("sensitiveUri" + i, "");
                map.put("sensitiveApiCode" + i, "");
                map.put("sensitiveApiName" + i, "");
                map.put("outputSensitiveType" + i, "");
                map.put("sensitiveRisk" + i, "");
                map.put("sensitiveAk" + i, "");
            }
        }

        if (sensitiveApiCodeNum > 0) {
            map.put("sensitiveMaxApiCode", "其中接口" + sensitiveMaxApiCode + "输出敏感数据条数最多"); //输出敏感数量最多的接口
            map.put("sensitiveMaxNum", "，为 " + sensitiveMaxNum + " 条。"); //最多的接口敏感输出量
            map.put("interfaceOutputSensitive", "接口编码及输出敏感数据类型如下:" + interfaceOutputSensitive);
        }
    }


    /**
     * 失活API
     *
     * @param beginDate          开始日期
     * @param endDate            结束日期
     * @param icInterfaceInfoMap ic接口信息图
     * @param map                地图
     * @return int
     */
    private int deactivationAPIDetection(String beginDate, String endDate, Map<String, String> icInterfaceInfoMap, HashMap<String, Object> map, List<String> domainApiCodeList) {
        Set<String> recentlyDeactivatedApiSet = new HashSet<>();
        String apiUnusedCheckResultApiCode = "";
        int queryQuantity = 0;

        // 分域结果集
        String inactivationLatestTime = apiUnusedcheckresultService.queryLatestTime(beginDate, endDate, domainApiCodeList);
        if (StringUtils.isNotBlank(inactivationLatestTime)) {
            String inactivationOneDayLate = DateUtils.dealDays(inactivationLatestTime, 1);

            List<ApiUnusedcheckresult> apiUnusedCheckResults = apiUnusedcheckresultService.queryLatestUnusedCheckResult(inactivationLatestTime.substring(0, 9), inactivationOneDayLate);

            // 分域结果集
            apiUnusedCheckResults = apiUnusedCheckResults.stream()
                    .filter(item -> domainApiCodeList.contains(item.getApicode()))
                    .collect(Collectors.toList());

            queryQuantity = apiUnusedCheckResults.size();

            for (int i = 0; i < Math.min(apiUnusedCheckResults.size(), 5); i++) {
                ApiUnusedcheckresult result = apiUnusedCheckResults.get(i);
                String apiUnusedCheckUri = result.getApiurl(); //接口URI
                String apiUnusedCheckApiCode = result.getApicode(); //接口编码
                String apiUnusedCheckApiName = result.getApiname(); //接口名称
                String apiUnusedCheckRisk = result.getRisk(); //风险程度

                map.put("apiUnusedCheckUri" + i, apiUnusedCheckUri);
                map.put("apiUnusedCheckApiCode" + i, apiUnusedCheckApiCode);
                map.put("apiUnusedCheckApiName" + i, apiUnusedCheckApiName);
                map.put("apiUnusedCheckRisk" + i, RiskEnum.getRisk(apiUnusedCheckRisk));

                recentlyDeactivatedApiSet.add(apiUnusedCheckApiCode);
            }

            for (int i = apiUnusedCheckResults.size(); i < 5; i++) {
                map.put("apiUnusedCheckApiCode" + i, "");
                map.put("apiUnusedCheckUri" + i, "");
                map.put("apiUnusedCheckApiName" + i, "");
                map.put("apiUnusedCheckRisk" + i, "");
            }

            apiUnusedCheckResultApiCode = StringUtils.join(recentlyDeactivatedApiSet, ",");
        }

        map.put("apiUnusedCheckResultNum", queryQuantity); //失活API数量
        map.put("apiUnusedCheckResultApiCode", apiUnusedCheckResultApiCode); //失活API拼接
        return queryQuantity;
    }


    /**
     * API调用错误检测
     *
     * @param beginDate          开始日期
     * @param endDate            结束日期
     * @param icInterfaceInfoMap ic接口信息图
     * @param map                地图
     */
    private void apiCallErrorDetection(String beginDate, String endDate, Map<String, String> icInterfaceInfoMap, HashMap<String, Object> map, List<String> domainApiCodeList) {
        List<ApiErrorcheckresult> apiErrorCheckResults = apiErrorcheckresultService.queryContentResult(beginDate, endDate);

        // 分域结果集
        apiErrorCheckResults = apiErrorCheckResults.stream()
                .filter(item -> domainApiCodeList.contains(item.getApicode()))
                .collect(Collectors.toList());

        if (!apiErrorCheckResults.isEmpty()) {
            int size = Math.min(apiErrorCheckResults.size(), 5); // Determine loop limit
            for (int i = 0; i < size; i++) {
                ApiErrorcheckresult errorResult = apiErrorCheckResults.get(i);
                String apiErrorCheckIp = errorResult.getReqip(); //请求IP
                String apiErrorCheckUri = errorResult.getApiurl(); //请求URI
                String apiErrorCheckApiCode = errorResult.getApicode(); //接口编码
                String apiErrorCheckApiName = errorResult.getApiname(); //接口名称
                String apiErrorCheckType = errorResult.getErrortype(); //错误类型
                Integer apiErrorCheckCount = errorResult.getErrorcount(); //错误次数
                String apiErrorCheckRisk = Const.RISK_MIDDLE; //风险程度
                String apiErrorCheckAk = errorResult.getAk(); //风险程度

                map.put("apiErrorCheckIp" + i, apiErrorCheckIp);
                map.put("apiErrorCheckUri" + i, apiErrorCheckUri);
                map.put("apiErrorCheckApiCode" + i, apiErrorCheckApiCode);
                map.put("apiErrorCheckApiName" + i, apiErrorCheckApiName);
                map.put("apiErrorCheckType" + i, apiErrorCheckType);
                map.put("apiErrorCheckCount" + i, apiErrorCheckCount);
                map.put("apiErrorCheckRisk" + i, RiskEnum.getRisk(apiErrorCheckRisk));
                map.put("apiErrorCheckAk" + i, apiErrorCheckAk);
            }

            for (int i = apiErrorCheckResults.size(); i < 5; i++) {
                map.put("apiErrorCheckIp" + i, "");
                map.put("apiErrorCheckUri" + i, "");
                map.put("apiErrorCheckApiCode" + i, "");
                map.put("apiErrorCheckApiName" + i, "");
                map.put("apiErrorCheckType" + i, "");
                map.put("apiErrorCheckCount" + i, "");
                map.put("apiErrorCheckRisk" + i, "");
                map.put("apiErrorCheckAk" + i, "");
            }
        }

        List<ApiErrorcheckresult> errorCheckApiCode = new ArrayList<>(apiErrorCheckResults.stream()
                .collect(Collectors.toMap(ApiErrorcheckresult::getApicode, Function.identity(), (a, b) -> a))
                .values());
        map.put("errorCheckApiCode", errorCheckApiCode.size());

        String errorCheckMaxApiCode = "";
        String errorCheckMaxNum = "";
        List<Map<String, Object>> errorCheckNunMapList = apiErrorcheckresultService.queryErrorCheckNum(beginDate, endDate);
        Map<String, Integer> errorCheckNunMap = new HashMap<>();
        if (!errorCheckNunMapList.isEmpty()) {
            for (Map<String, Object> errorNumMsg : errorCheckNunMapList) {
                String apiCode = (String) errorNumMsg.get("apicode");
                Integer num = ((BigDecimal) errorNumMsg.get("num")).intValue();
                // 分域结果集
                if (domainApiCodeList.contains(apiCode)) {
                    errorCheckNunMap.put(apiCode, num);
                }
            }

            errorCheckMaxApiCode = findMapMaxKey(errorCheckNunMap);
            errorCheckMaxNum = String.valueOf(errorCheckNunMap.get(errorCheckMaxApiCode));

            map.put("errorCheckMaxApiCode", ",其中" + errorCheckMaxApiCode + "错误次数"); //接口调用错误告警最多的接口
            map.put("errorCheckMaxNum", errorCheckMaxNum + "最多"); //接口调用错误最多的接口的条数
        }
    }


    /**
     * 接口攻击检测
     *
     * @param beginDate          开始日期
     * @param endDate            结束日期
     * @param icInterfaceInfoMap ic接口信息图
     * @param map                地图
     */
    private void interfaceAttackDetection(String beginDate, String endDate, Map<String, String> icInterfaceInfoMap, HashMap<String, Object> map, List<String> domainApiCodeList) {
        List<ApiAttackdetection> apiAttackDetections = apiAttackdetectionService.queryContentResult(beginDate, endDate);

        // 分域结果集
        apiAttackDetections = apiAttackDetections.stream()
                .filter(item -> domainApiCodeList.contains(item.getApicode()))
                .collect(Collectors.toList());

        List<String> attackDetectionList = new ArrayList<>();
        int dataSize = Math.min(apiAttackDetections.size(), 5);
        for (int i = 0; i < dataSize; i++) {
            ApiAttackdetection attackDetection = apiAttackDetections.get(i);
            map.put("attackDetectionAk" + i, attackDetection.getAk());
            map.put("attackDetectionIp" + i, attackDetection.getReqip());
            map.put("attackDetectionApiCode" + i, attackDetection.getApicode());
            map.put("attackDetectionApiName" + i, attackDetection.getApiname());
            map.put("attackDetectionUrl" + i, attackDetection.getUrl());
            map.put("attackDetectionType" + i, attackDetection.getAttacktype());
            map.put("attackDetectionCallDate" + i, attackDetection.getCalldate());
            map.put("attackDetectionRisk" + i, RiskEnum.getRisk(attackDetection.getRisk()));
            attackDetectionList.add(attackDetection.getApicode());
        }

        for (int i = dataSize; i < 5; i++) {
            map.put("attackDetectionAk" + i, "");
            map.put("attackDetectionIp" + i, "");
            map.put("attackDetectionApiCode" + i, "");
            map.put("attackDetectionApiName" + i, "");
            map.put("attackDetectionUrl" + i, "");
            map.put("attackDetectionType" + i, "");
            map.put("attackDetectionCallDate" + i, "");
            map.put("attackDetectionRisk" + i, "");
        }

        String attackDetectionInterface = "";
        if (!attackDetectionList.isEmpty()) {
            StringBuilder stringBuilder = new StringBuilder();
            for (String apicode : attackDetectionList) {
                stringBuilder.append(apicode).append(",");
            }
            attackDetectionInterface = stringBuilder.substring(0, stringBuilder.length() - 1); // Remove the last comma
        }

        map.put("attackDetectionInterface", "周期内共检测出存在接口攻击API " + apiAttackDetections.size() + "个, " + attackDetectionInterface);
    }


    /**
     * 非工作时段调用
     *
     * @param beginDate          开始日期
     * @param endDate            结束日期
     * @param icInterfaceInfoMap ic接口信息图
     * @param map                地图
     */
    private void callDuringNonWorkingHours(String beginDate, String endDate, Map<String, String> icInterfaceInfoMap, HashMap<String, Object> map, List<String> domainApiCodeList) {
        List<ApiNonperiodcall> apiNonPeriodCalls = apiNonperiodcallService.queryContentResult(beginDate, endDate);

        // 分域结果集
        apiNonPeriodCalls = apiNonPeriodCalls.stream()
                .filter(item -> domainApiCodeList.contains(item.getApicode()))
                .collect(Collectors.toList());

        int dataSize = Math.min(apiNonPeriodCalls.size(), 5);
        for (int i = 0; i < dataSize; i++) {
            ApiNonperiodcall nonPeriodCall = apiNonPeriodCalls.get(i);
            map.put("apiNonPeriodCallAk" + i, nonPeriodCall.getAk());
            map.put("apiNonPeriodCallIP" + i, nonPeriodCall.getReqip());
            map.put("apiNonPeriodCallUri" + i, nonPeriodCall.getApiurl());
            map.put("apiNonPeriodCallApiCode" + i, nonPeriodCall.getApicode());
            map.put("apiNonPeriodCallApiName" + i, nonPeriodCall.getApiname());
            map.put("normalCallPeriod" + i, nonPeriodCall.getNormalcallperiod());
            map.put("abnormalCallPeriod" + i, nonPeriodCall.getAbnormalcallperiod());
            map.put("apiNonPeriodCallNum" + i, nonPeriodCall.getCallnum());
            map.put("apiNonPeriodCallRisk" + i, RiskEnum.getRisk(nonPeriodCall.getRisk()));
        }

        for (int i = dataSize; i < 5; i++) {
            map.put("apiNonPeriodCallAk" + i, "");
            map.put("apiNonPeriodCallIP" + i, "");
            map.put("apiNonPeriodCallUri" + i, "");
            map.put("apiNonPeriodCallApiCode" + i, "");
            map.put("apiNonPeriodCallApiName" + i, "");
            map.put("normalCallPeriod" + i, "");
            map.put("abnormalCallPeriod" + i, "");
            map.put("apiNonPeriodCallNum" + i, "");
            map.put("apiNonPeriodCallRisk" + i, "");
        }

        Set<String> uniqueApiCodes = apiNonPeriodCalls.stream()
                .map(ApiNonperiodcall::getApicode)
                .collect(Collectors.toSet());
        map.put("nonPeriodCallNum", uniqueApiCodes.size());
    }


    /**
     * 不常调用API
     *
     * @param beginDate          开始日期
     * @param endDate            结束日期
     * @param icInterfaceInfoMap ic接口信息图
     * @param map                地图
     */
    private void notFrequentlyCallingAPIs(String beginDate, String endDate, Map<String, String> icInterfaceInfoMap, HashMap<String, Object> map, List<String> domainApiCodeList) {
        List<ApiInfrequentlycall> apiInfrequentlyCalls = apiInfrequentlycallService.queryContentResult(beginDate, endDate);

        // 分域结果集
        apiInfrequentlyCalls = apiInfrequentlyCalls.stream()
                .filter(item -> domainApiCodeList.contains(item.getApicode()))
                .collect(Collectors.toList());

        int dataSize = Math.min(apiInfrequentlyCalls.size(), 5);

        for (int i = 0; i < dataSize; i++) {
            ApiInfrequentlycall infrequentlyCall = apiInfrequentlyCalls.get(i);
            String apiCode = infrequentlyCall.getApicode();
            String apiName = infrequentlyCall.getSparefield3();
            String callNum = infrequentlyCall.getCallcount();
            String risk = infrequentlyCall.getRisk();
            String apiInfrequentlyCallAk = infrequentlyCall.getAk();

            map.put("apiInfrequentlyCallAk" + i, apiInfrequentlyCallAk);
            map.put("apiInfrequentlyCallApiCode" + i, apiCode);
            map.put("apiInfrequentlyCallApiName" + i, apiName);
            map.put("apiInfrequentlyCallNum" + i, callNum);
            map.put("apiInfrequentlyCallRisk" + i, RiskEnum.getRisk(risk));
        }

        for (int i = dataSize; i < 5; i++) {
            map.put("apiInfrequentlyCallAk" + i, "");
            map.put("apiInfrequentlyCallApiCode" + i, "");
            map.put("apiInfrequentlyCallApiName" + i, "");
            map.put("apiInfrequentlyCallNum" + i, "");
            map.put("apiInfrequentlyCallRisk" + i, "");
        }

        // 获取最近检测时间当日不常调用API及数量
        Set<String> latestInfrequentlyApiCodeSet = new HashSet<>();
        // 分域结果集
        String latestTime = apiInfrequentlycallService.queryLatestTime(beginDate, endDate, domainApiCodeList);
        if (StringUtils.isNotBlank(latestTime)) {
            String oneDayLater = DateUtils.dealDays(latestTime, 1);
            List<String> latestInfrequentlyApiCodes = apiInfrequentlycallService.queryLatestInfrequentlyCall(latestTime.substring(0, 9), oneDayLater, domainApiCodeList);
            latestInfrequentlyApiCodeSet.addAll(latestInfrequentlyApiCodes);
        }

        map.put("latestInfrequentlyNum", latestInfrequentlyApiCodeSet.size());
        map.put("latestInfrequentlyApiCode", StringUtils.join(latestInfrequentlyApiCodeSet, ","));
    }


    /**
     * API调用量
     *
     * @param beginDate          开始日期
     * @param endDate            结束日期
     * @param icInterfaceInfoMap ic接口信息图
     * @param map                地图
     */
    private void apiCallVolume(String beginDate, String endDate, Map<String, String> icInterfaceInfoMap, HashMap<String, Object> map, List<String> domainApiCodeList) {
        List<IcInvokingRecord> icInvokingRecordWarnings = icInvokingRecordService.queryContentResult(beginDate, endDate);

        // 分域结果集
        icInvokingRecordWarnings = icInvokingRecordWarnings.stream()
                .filter(item -> domainApiCodeList.contains(item.getApicode()))
                .collect(Collectors.toList());

        int dataSize = Math.min(icInvokingRecordWarnings.size(), 5);

        for (int i = 0; i < dataSize; i++) {
            IcInvokingRecord invokingRecord = icInvokingRecordWarnings.get(i);
            map.put("icInvokingRecordAk" + i, invokingRecord.getSparefield1()); //ak
            map.put("icInvokingRecordIp" + i, invokingRecord.getReqip());
            map.put("icInvokingRecordUri" + i, invokingRecord.getApiurl());
            map.put("icInvokingRecordApiCode" + i, invokingRecord.getApicode());
            map.put("icInvokingRecordApiName" + i, invokingRecord.getSparefield3());
            map.put("icInvokingRecordNum" + i, invokingRecord.getNum());
            map.put("icInvokingRecordDate" + i, invokingRecord.getDate());
            map.put("icInvokingRecordRisk" + i, RiskEnum.getRisk(invokingRecord.getRisk()));
        }

        for (int i = dataSize; i < 5; i++) {
            map.put("icInvokingRecordAk" + i, "");
            map.put("icInvokingRecordIp" + i, "");
            map.put("icInvokingRecordUri" + i, "");
            map.put("icInvokingRecordApiCode" + i, "");
            map.put("icInvokingRecordApiName" + i, "");
            map.put("icInvokingRecordNum" + i, "");
            map.put("icInvokingRecordDate" + i, "");
            map.put("icInvokingRecordRisk" + i, "");
        }

        String maxCallApiCode = "";
        String maxCallDate = "";
        Long maxCallNum = 0L;

        // 分域结果集
        IcInvokingRecord icInvokingRecordWarning = icInvokingRecordService.maximumNumberOfCalls(beginDate, endDate, domainApiCodeList);

        if (icInvokingRecordWarning != null) {
            maxCallApiCode = icInvokingRecordWarning.getApicode();
            maxCallDate = icInvokingRecordWarning.getDate();
            maxCallNum = icInvokingRecordWarning.getNum();
        }

        int interfaceLogSum = 0;
        int interfaceNum = 0;
        try {
            // 分域结果集
            interfaceLogSum = icInvokingRecordService.queryTotalLogs(beginDate, endDate, domainApiCodeList);
            interfaceNum = icInvokingRecordService.queryTotalApiCode(beginDate, endDate, domainApiCodeList);
        } catch (Exception e) {
            interfaceLogSum = 0;
            interfaceNum = 0;
        }
        map.put("interfaceLogSum", interfaceLogSum); //调用日志数量
        map.put("interfaceNum", interfaceNum); //周期调用接口数

        if (interfaceNum > 0) {
            map.put("maxCallApiCode", ",其中" + maxCallApiCode + "在");
            map.put("maxCallDate", maxCallDate + "调用次数最多");
            map.put("maxCallNum", "为 " + maxCallNum + "次");
        }
    }


    /**
     * 用户API调用量
     *
     * @param beginDate          开始日期
     * @param endDate            结束日期
     * @param icInterfaceInfoMap ic接口信息图
     * @param map                地图
     */
    private void userAPICallVolume(String beginDate, String endDate, Map<String, String> icInterfaceInfoMap, HashMap<String, Object> map, List<String> domainApiCodeList) {
        String userCallClient = "";
        String maxCallClientIp = "";
        List<ApiUsercall> apiUserCalls = apiUsercallService.queryContentResult(beginDate, endDate);

        // 分域结果集
        apiUserCalls = apiUserCalls.stream()
                .filter(item -> domainApiCodeList.contains(item.getAccount()))
                .collect(Collectors.toList());

        if (!apiUserCalls.isEmpty()) {
            int dataSize = Math.min(apiUserCalls.size(), 5);

            for (int i = 0; i < dataSize; i++) {
                ApiUsercall apiUserCall = apiUserCalls.get(i);
                String userCallClientIp = apiUserCall.getClientip(); //客户端IP
                String userCallUri = apiUserCall.getApiurl(); //请求URI
                String userCallApiCode = apiUserCall.getAccount(); //接口编码
                String userCallApiName = apiUserCall.getApiname(); //接口名称
                String userCallCount = apiUserCall.getCallcount(); //当日调用次数
                String userCallDate = apiUserCall.getCalldate(); //调用日期
                String userCallRisk = apiUserCall.getRisk(); //风险程度

                map.put("userCallUri" + i, userCallUri);
                map.put("userCallClientIp" + i, userCallClientIp);
                map.put("userCallApiCode" + i, userCallApiCode);
                map.put("userCallApiName" + i, userCallApiName);
                map.put("userCallCount" + i, userCallCount);
                map.put("userCallDate" + i, userCallDate);
                map.put("userCallRisk" + i, RiskEnum.getRisk(userCallRisk));
            }

            for (int i = dataSize; i < 5; i++) {
                map.put("userCallUri" + i, "");
                map.put("userCallClientIp" + i, "");
                map.put("userCallApiCode" + i, "");
                map.put("userCallApiName" + i, "");
                map.put("userCallCount" + i, "");
                map.put("userCallDate" + i, "");
                map.put("userCallRisk" + i, "");
            }

            Set<String> userCallClientSet = new HashSet<>();
            for (ApiUsercall apiUserCall : apiUserCalls) {
                userCallClientSet.add(apiUserCall.getClientip());
            }
            userCallClient = StringUtils.join(userCallClientSet, ","); //异常客户端IP

            Optional<ApiUsercall> apiUsercallMax = apiUserCalls.stream()
                    .max(Comparator.comparingInt(o -> Integer.parseInt(o.getCallcount())));
            if (apiUsercallMax.isPresent()) {
                maxCallClientIp = apiUsercallMax.get().getClientip();
            }
        }
        if (StringUtils.isNotBlank(userCallClient)) {
            map.put("userCallClient", "周期检测到用户调用API异常的客户端IP有: " + userCallClient);
            map.put("maxCallClientIp", ",其中" + maxCallClientIp + "请求次数较多。");
        }
    }


    /**
     * 静默账号突发访问API
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param map       地图
     */
    private void silentAccountSuddenAccessAPI(String beginDate, String endDate, HashMap<String, Object> map, List<String> domainApiCodeList) {
        List<ApiSilentaccountcall> apiSilentAccountCalls = apiSilentaccountcallService.queryContentResult(beginDate, endDate);

        // 分域结果集
        apiSilentAccountCalls = apiSilentAccountCalls.stream()
                .filter(item -> domainApiCodeList.contains(item.getApicode()))
                .collect(Collectors.toList());

        String silentAccounts = "";
        String accountAccessInterfaceMax = "";
        Set<String> silentAccountList = new HashSet<>();

        if (!apiSilentAccountCalls.isEmpty()) {
            int dataSize = Math.min(apiSilentAccountCalls.size(), 5);

            for (int i = 0; i < dataSize; i++) {
                ApiSilentaccountcall apiSilentAccountCall = apiSilentAccountCalls.get(i);
                String apiSilentAccountCallIp = apiSilentAccountCall.getReqip(); //请求IP
                String apiSilentAccount = apiSilentAccountCall.getAccount(); //账号
                String apiSilentAccountCallApiCode = apiSilentAccountCall.getApicode(); //接口编码
                String apiSilentAccountCallApiName = apiSilentAccountCall.getApiname(); //接口名称
                String apiSilentAccountCallUri = apiSilentAccountCall.getApiurl(); //接口URI
                String apiSilentAccountCallNum = apiSilentAccountCall.getCallnum(); //当日调用量
                String apiSilentAccountCallRisk = apiSilentAccountCall.getRisk(); //风险程度
                String apiSilentAccountCallAk = apiSilentAccountCall.getAk(); //AK

                map.put("apiSilentAccountCallAk" + i, apiSilentAccountCallAk);
                map.put("apiSilentAccountCallNum" + i, apiSilentAccountCallNum);
                map.put("apiSilentAccountCallIp" + i, apiSilentAccountCallIp);
                map.put("apiSilentAccountCallUri" + i, apiSilentAccountCallUri);
                map.put("apiSilentAccount" + i, apiSilentAccount);
                map.put("apiSilentAccountCallApiCode" + i, apiSilentAccountCallApiCode);
                map.put("apiSilentAccountCallApiName" + i, apiSilentAccountCallApiName);
                map.put("apiSilentAccountCallRisk" + i, RiskEnum.getRisk(apiSilentAccountCallRisk));

                silentAccountList.add(apiSilentAccountCallIp);
            }

            for (int i = dataSize; i < 5; i++) {
                map.put("apiSilentAccountCallAk" + i, "");
                map.put("apiSilentAccountCallNum" + i, "");
                map.put("apiSilentAccountCallUri" + i, "");
                map.put("apiSilentAccountCallIp" + i, "");
                map.put("apiSilentAccount" + i, "");
                map.put("apiSilentAccountCallApiCode" + i, "");
                map.put("apiSilentAccountCallApiName" + i, "");
                map.put("apiSilentAccountCallRisk" + i, "");
            }

            Map<String, Integer> accountApiCodeMap = new HashMap<>();
            for (ApiSilentaccountcall apiSilentAccountCall : apiSilentAccountCalls) {
                String key = apiSilentAccountCall.getReqip();
                int valueToAdd = Integer.parseInt(apiSilentAccountCall.getCallnum());
                if (accountApiCodeMap.containsKey(key)) {
                    Integer existingValue = accountApiCodeMap.get(key);
                    valueToAdd += existingValue;
                }
                accountApiCodeMap.put(key, valueToAdd);
            }

            silentAccounts = StringUtils.join(silentAccountList, ","); //静默账号
            accountAccessInterfaceMax = findMapMaxKey(accountApiCodeMap);
        }

        // 无静默账号,即不输出后续数据
        if (StringUtils.isNotBlank(silentAccounts)) {
            map.put("silentAccounts", "周期检测到静默账号有: " + silentAccounts);
            map.put("accountAccessInterfaceMax", ",其中" + accountAccessInterfaceMax + "请求接口较多。");
        }
    }


    /**
     * TODO 接口规范审计[海南无]
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param map       地图
     */
    private void interfaceSpecificationAudit(String beginDate, String endDate, HashMap<String, Object> map, List<String> domainApiCodeList) {
        String complianceAuditInterface = "";
        List<ApiComplianceaudit> apiComplianceAudits = apiComplianceauditService.queryContentResult(beginDate, endDate);

        // 分域结果集
        apiComplianceAudits = apiComplianceAudits.stream()
                .filter(item -> domainApiCodeList.contains(item.getApicode()))
                .collect(Collectors.toList());

        if (!apiComplianceAudits.isEmpty()) {
            int dataSize = Math.min(apiComplianceAudits.size(), 5);

            for (int i = 0; i < dataSize; i++) {
                ApiComplianceaudit apiComplianceAudit = apiComplianceAudits.get(i);
                String complianceAuditApiCode = apiComplianceAudit.getApicode();
                String complianceAuditApiName = apiComplianceAudit.getApiname();
                String complianceAuditCompliance = apiComplianceAudit.getCompliance();
                String compliance = getComplianceString(complianceAuditCompliance);
                String complianceAuditRisk = apiComplianceAudit.getRisk();

                map.put("complianceAuditApiCode" + i, complianceAuditApiCode);
                map.put("complianceAuditApiName" + i, complianceAuditApiName);
                map.put("complianceAuditCompliance" + i, compliance);
                map.put("complianceAuditRisk" + i, RiskEnum.getRisk(complianceAuditRisk));
            }

            for (int i = dataSize; i < 5; i++) {
                map.put("complianceAuditApiCode" + i, "");
                map.put("complianceAuditApiName" + i, "");
                map.put("complianceAuditCompliance" + i, "");
                map.put("complianceAuditRisk" + i, "");
            }

            Set<String> complianceAuditInterfaceSet = apiComplianceAudits.stream()
                    .map(ApiComplianceaudit::getApicode)
                    .collect(Collectors.toSet());
            complianceAuditInterface = StringUtils.join(complianceAuditInterfaceSet, ",");
            map.put("complianceAuditInterface", complianceAuditInterface);
        }
    }

    private String getComplianceString(String compliance) {
        switch (compliance) {
            case Const.INTERFACE_COMPLIANCE_AUDIT_DICTIONARIES_COMPLIANCE:
                return Const.INTERFACE_COMPLIANCE_AUDIT_COMPLIANCE;
            case Const.INTERFACE_COMPLIANCE_AUDIT_PARTIAL_DICTIONARIES_COMPLIANCE:
                return Const.INTERFACE_COMPLIANCE_AUDIT_PARTIAL_COMPLIANCE;
            case Const.INTERFACE_COMPLIANCE_AUDIT_NON_DICTIONARIES_CONFORMANCE:
                return Const.INTERFACE_COMPLIANCE_AUDIT_NON_CONFORMANCE;
            default:
                return "";
        }
    }


    /**
     * 接口鉴权检测
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param map       地图
     */
    private void interfaceAuthenticationDetection(String beginDate, String endDate, HashMap<String, Object> map, List<String> domainApiCodeList) {
        String authCheckResultInterface = "";
        List<ApiAuthcheckresult> apiAuthCheckResults = apiAuthcheckresultService.queryContentResult(beginDate, endDate);

        // 分域结果集
        apiAuthCheckResults = apiAuthCheckResults.stream()
                .filter(item -> domainApiCodeList.contains(item.getApicode()))
                .collect(Collectors.toList());

        if (!apiAuthCheckResults.isEmpty()) {
            int dataSize = Math.min(apiAuthCheckResults.size(), 5);

            for (int i = 0; i < dataSize; i++) {
                ApiAuthcheckresult apiAuthCheckResult = apiAuthCheckResults.get(i);
                String authCheckResultApiCode = apiAuthCheckResult.getApicode();
                String authCheckResultApiName = apiAuthCheckResult.getApiname();
                String authCheckResultMethod = apiAuthCheckResult.getAuthmethod();
                String authCheckResultRisk = apiAuthCheckResult.getRisk();
                String authCheckResultIP = apiAuthCheckResult.getReqip();
                String authCheckResultUri = apiAuthCheckResult.getUrl();
                String authCheckResultAk = apiAuthCheckResult.getAk();

                map.put("authCheckResultAk" + i, authCheckResultAk);
                map.put("authCheckResultIP" + i, authCheckResultIP);
                map.put("authCheckResultUri" + i, authCheckResultUri);
                map.put("authCheckResultApiCode" + i, authCheckResultApiCode);
                map.put("authCheckResultApiName" + i, authCheckResultApiName);
                map.put("authCheckResultMethod" + i, authCheckResultMethod);
                map.put("authCheckResultRisk" + i, RiskEnum.getRisk(authCheckResultRisk));
            }

            for (int i = dataSize; i < 5; i++) {
                map.put("authCheckResultAk" + i, "");
                map.put("authCheckResultIP" + i, "");
                map.put("authCheckResultUri" + i, "");
                map.put("authCheckResultApiCode" + i, "");
                map.put("authCheckResultApiName" + i, "");
                map.put("authCheckResultMethod" + i, "");
                map.put("authCheckResultRisk" + i, "");
            }

            // 计算异常接口并设置异常接口字符串
            Set<String> authCheckResultInterfaceSet = apiAuthCheckResults.stream().map(ApiAuthcheckresult::getApicode).collect(Collectors.toSet());
            authCheckResultInterface = StringUtils.join(authCheckResultInterfaceSet, ",");

            // 如果存在异常接口，则添加到输出结果中
            if (StringUtils.isNotBlank(authCheckResultInterface)) {
                map.put("authCheckResultInterface", " 周期检测到接口鉴权异常的接口编码有" + authCheckResultInterface + " 。");
            }
        }
    }


    /**
     * 突发获取大量数据
     *
     * @param beginDate          开始日期
     * @param endDate            结束日期
     * @param icInterfaceInfoMap ic接口信息图
     * @param map                地图
     */
    private void suddenAcquisitionOfLargeAmountsOfData(String beginDate, String endDate, Map<String, String> icInterfaceInfoMap, HashMap<String, Object> map, List<String> domainApiCodeList) {
        List<ApiOutdatacall> apiOutDataCalls = apiOutdatacallService.queryContentResult(beginDate, endDate);

        // 分域结果集
        apiOutDataCalls = apiOutDataCalls.stream()
                .filter(item -> domainApiCodeList.contains(item.getApicode()))
                .collect(Collectors.toList());

        int apiOutDataCallApiCodeNum = 0;

        if (!apiOutDataCalls.isEmpty()) {
            int dataSize = Math.min(apiOutDataCalls.size(), 5);

            for (int i = 0; i < dataSize; i++) {
                ApiOutdatacall apiOutDataCall = apiOutDataCalls.get(i);
                String apiOutDataCallApiCode = apiOutDataCall.getApicode();
                String apiOutDataCallApiName = apiOutDataCall.getApiname();
                String apiOutDataCallResultCount = apiOutDataCall.getResultcount();
                String apiOutDataCallRisk = apiOutDataCall.getRisk();
                String apiOutDataCallIp = apiOutDataCall.getReqip();
                String apiOutDataCallUri = apiOutDataCall.getApiurl();
                String apiOutDataCallAk = apiOutDataCall.getAk();

                map.put("apiOutDataCallAk" + i, apiOutDataCallAk);
                map.put("apiOutDataCallIp" + i, apiOutDataCallIp);
                map.put("apiOutDataCallUri" + i, apiOutDataCallUri);
                map.put("apiOutDataCallApiCode" + i, apiOutDataCallApiCode);
                map.put("apiOutDataCallApiName" + i, apiOutDataCallApiName);
                map.put("apiOutDataCallResultCount" + i, apiOutDataCallResultCount);
                map.put("apiOutDataCallRisk" + i, RiskEnum.getRisk(apiOutDataCallRisk));
            }

            for (int i = dataSize; i < 5; i++) {
                map.put("apiOutDataCallAk" + i, "");
                map.put("apiOutDataCallIp" + i, "");
                map.put("apiOutDataCallUri" + i, "");
                map.put("apiOutDataCallApiCode" + i, "");
                map.put("apiOutDataCallApiName" + i, "");
                map.put("apiOutDataCallResultCount" + i, "");
                map.put("apiOutDataCallRisk" + i, "");
            }

            // 记录异常接口编码
            Set<String> abnormalInterface = apiOutDataCalls.stream().map(ApiOutdatacall::getApicode).collect(Collectors.toSet());

            // 分域结果集
            apiOutDataCallApiCodeNum = apiOutdatacallService.apiOutDataCallApiCodeNum(beginDate, endDate, domainApiCodeList);
        }

        map.put("apiOutDataCallApiCodeNum", apiOutDataCallApiCodeNum);
    }


    /**
     * 接口出现未预期内容
     *
     * @param beginDate          开始日期
     * @param endDate            结束日期
     * @param map                地图
     * @param icInterfaceInfoMap ic接口信息图
     */
    private void unexpectedContentInTheInterface(String beginDate, String endDate, HashMap<String, Object> map, Map<String, String> icInterfaceInfoMap, List<String> domainApiCodeList) {
        // 2.1 获取接口未预期内容数量信息
        Map<String, Integer> unExpectedContentMap = new HashMap<>();
        List<Map<String, Object>> unExpectedContentMapsList = apiUnexpectedcontentresultService.queryUnExpectedContentNum(beginDate, endDate);
        String[] unExpectedContentMaxApiCode = {""};
        int[] unExpectedContentMaxNum = {0};

        for (Map<String, Object> unExpectedContent : unExpectedContentMapsList) {
            String apiCode = (String) unExpectedContent.get("apicode");
            int num = ((BigInteger) unExpectedContent.get("num")).intValue();
            // 分域结果集
            if (domainApiCodeList.contains(apiCode)) {
                unExpectedContentMap.put(apiCode, num);
            }
        }

        if (!unExpectedContentMap.isEmpty()) {
            unExpectedContentMaxApiCode[0] = Collections.max(unExpectedContentMap.entrySet(), Map.Entry.comparingByValue()).getKey();
            unExpectedContentMaxNum[0] = unExpectedContentMap.get(unExpectedContentMaxApiCode[0]);

            map.put("unExpectedContentMaxApiCode", unExpectedContentMaxApiCode[0]);
            map.put("unExpectedContentMaxNum", unExpectedContentMaxNum[0]);
        }

        // 2.2 处理接口未预期内容表格相关数据
        List<ApiUnexpectedcontentresult> apiUnexpectedcontentresults = apiUnexpectedcontentresultService.queryContentResult(beginDate, endDate);

        // 分域结果集
        apiUnexpectedcontentresults = apiUnexpectedcontentresults.stream()
                .filter(item -> domainApiCodeList.contains(item.getApicode()))
                .collect(Collectors.toList());

        int size = apiUnexpectedcontentresults.size();

        for (int i = 0; i < size; i++) {
            ApiUnexpectedcontentresult apiUnexpectedcontentresult = apiUnexpectedcontentresults.get(i);
            String unExpectedContentResultApiCode = apiUnexpectedcontentresult.getApicode();
            String unExpectedContentResultApiName = icInterfaceInfoMap.getOrDefault(unExpectedContentResultApiCode, "");
            String unExpectedContentResultCheckObject = apiUnexpectedcontentresult.getCheckobject();
            String unExpectedContentResult = apiUnexpectedcontentresult.getUnexpectedcontent();
            String unExpectedContentResultRisk = apiUnexpectedcontentresult.getRisk();

            map.put("unExpectedContentResultApiCode" + i, unExpectedContentResultApiCode);
            map.put("unExpectedContentResultApiName" + i, unExpectedContentResultApiName);
            map.put("unExpectedContentResultCheckObject" + i, unExpectedContentResultCheckObject);
            map.put("unExpectedContentResult" + i, unExpectedContentResult);
            map.put("unExpectedContentResultRisk" + i, RiskEnum.getRisk(unExpectedContentResultRisk));
        }

        // 补足表格数据
        for (int i = size; i < 5; i++) {
            map.put("unExpectedContentResultApiCode" + i, "");
            map.put("unExpectedContentResultApiName" + i, "");
            map.put("unExpectedContentResultCheckObject" + i, "");
            map.put("unExpectedContentResult" + i, "");
            map.put("unExpectedContentResultRisk" + i, "");
        }

        // 计算接口数量
        int unExpectedContentApiCodeNum = unExpectedContentMap.size();
        map.put("unExpectedContentApiCodeNum", unExpectedContentApiCodeNum);

        if (unExpectedContentApiCodeNum > 0) {
            map.computeIfAbsent("unExpectedContentMaxApiCode", k -> "其中接口" + unExpectedContentMaxApiCode[0] + "出现未预期内容条数最多,");
            map.computeIfAbsent("unExpectedContentMaxNum", k -> "为" + unExpectedContentMaxNum[0] + "条。");
        }
    }

    /**
     * 接口请求内容检测
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param map       数据集
     */
    private void interfaceRequestContentDetection(String beginDate, String endDate, HashMap<String, Object> map, List<String> domainApiCodeList) {
        //接口请求内容
        StringBuilder interfaceRequestContent = new StringBuilder();
        List<ApiInvolvesaccountpassword> interfaceRequestList = apiInvolvesaccountpasswordService.queryDescriptionInterfaceAlarmTypes(beginDate, endDate);

        // 分域结果集
        interfaceRequestList = interfaceRequestList.stream()
                .filter(item -> domainApiCodeList.contains(item.getApicode()))
                .collect(Collectors.toList());

        int listSize = interfaceRequestList.size();
        if (listSize > 0) {
            for (int i = 0; i < listSize; i++) {
                ApiInvolvesaccountpassword apiInvolvesaccountpassword = interfaceRequestList.get(i);
                String message = apiInvolvesaccountpassword.getApicode() + "(" + apiInvolvesaccountpassword.getSparefield1() + ")";
                interfaceRequestContent.append(message).append(",");

                map.put("requestcontentAk" + i, apiInvolvesaccountpassword.getAk());//Ak
                map.put("requestcontentIp" + i, apiInvolvesaccountpassword.getReqip());//IP
                map.put("requestcontentUri" + i, apiInvolvesaccountpassword.getApiurl());//URI
                map.put("requestcontentApiCode" + i, apiInvolvesaccountpassword.getApicode());//接口编码
                map.put("requestcontentApiName" + i, apiInvolvesaccountpassword.getApiname());//接口名称
                map.put("reportAnEmergencyType" + i, apiInvolvesaccountpassword.getSparefield1());//结果类型
                map.put("requestcontentRisk" + i, RiskEnum.getRisk(apiInvolvesaccountpassword.getRisk()));//风险程度
            }

            // 如果接口请求列表大小小于5，则填充空值
            for (int i = listSize; i < 5; i++) {
                map.put("requestcontentAk" + i, "");//Ak
                map.put("requestcontentIp" + i, "");//IP
                map.put("requestcontentUri" + i, "");//URI
                map.put("requestcontentApiCode" + i, "");
                map.put("requestcontentApiName" + i, "");
                map.put("reportAnEmergencyType" + i, "");
                map.put("requestcontentRisk" + i, "");
            }
        }

        //无接口鉴权异常的接口,即不输出后续数据
        if (interfaceRequestContent.length() > 0) {
            map.put("requestcontentInterface", " 周期检测到接口请求内容的接口编码有" + interfaceRequestContent + " 。");
        }
    }


    /**
     * 查询map中value对应的最大的key
     *
     * @param map 地图
     * @return {@link String}
     */
    public String findMapMaxKey(Map<String, Integer> map) {
        List<Entry<String, Integer>> list = new ArrayList(map.entrySet());
        Collections.sort(list, (o1, o2) -> (o1.getValue().intValue() - o2.getValue().intValue()));
        String max = null;
        try {
            max = list.get(list.size() - 1).getKey();
        } catch (Exception e) {
            max = "";
        }
        return max;
    }

}