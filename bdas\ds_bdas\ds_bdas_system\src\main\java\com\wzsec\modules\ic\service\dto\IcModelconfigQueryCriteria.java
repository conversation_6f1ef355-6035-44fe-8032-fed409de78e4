package com.wzsec.modules.ic.service.dto;

import lombok.Data;

import java.util.List;

import com.wzsec.annotation.Query;

/**
 * <AUTHOR>
 * @date 2022-05-13
 */
@Data
public class IcModelconfigQueryCriteria {

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object apicode;

    @Query
    private String forecastmethod;

    @Query
    private String forecastfieldname;

    @Query
    private String forecastfieldvalues;

    @Query
    private String status;

    @Query(blurry = "apicode,forecastmethod,forecastfieldname,forecastfieldvalues,status")
    private String blurry;
}
