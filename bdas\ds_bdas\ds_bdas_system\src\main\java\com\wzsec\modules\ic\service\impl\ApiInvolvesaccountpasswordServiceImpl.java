package com.wzsec.modules.ic.service.impl;

import com.wzsec.modules.ic.domain.ApiInvolvesaccountpassword;
import com.wzsec.modules.ic.domain.enums.RiskEnum;
import com.wzsec.modules.ic.repository.ApiInvolvesaccountpasswordRepository;
import com.wzsec.modules.ic.service.ApiInvolvesaccountpasswordService;
import com.wzsec.modules.ic.service.IcInterfaceInfoService;
import com.wzsec.modules.ic.service.dto.ApiInvolvesaccountpasswordDto;
import com.wzsec.modules.ic.service.dto.ApiInvolvesaccountpasswordQueryCriteria;
import com.wzsec.modules.ic.service.mapper.ApiInvolvesaccountpasswordMapper;
import com.wzsec.utils.FileUtil;
import com.wzsec.utils.PageUtil;
import com.wzsec.utils.QueryHelp;
import com.wzsec.utils.ValidationUtil;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-12-28
 */
@Service
//@CacheConfig(cacheNames = "apiInvolvesaccountpassword")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class ApiInvolvesaccountpasswordServiceImpl implements ApiInvolvesaccountpasswordService {

    private final ApiInvolvesaccountpasswordRepository apiInvolvesaccountpasswordRepository;

    private final ApiInvolvesaccountpasswordMapper apiInvolvesaccountpasswordMapper;

    private final IcInterfaceInfoService icInterfaceInfoService;

    private final Environment environment;

    public ApiInvolvesaccountpasswordServiceImpl(ApiInvolvesaccountpasswordRepository apiInvolvesaccountpasswordRepository,
                                                 ApiInvolvesaccountpasswordMapper apiInvolvesaccountpasswordMapper,
                                                 IcInterfaceInfoService icInterfaceInfoService,
                                                 Environment environment) {
        this.apiInvolvesaccountpasswordRepository = apiInvolvesaccountpasswordRepository;
        this.apiInvolvesaccountpasswordMapper = apiInvolvesaccountpasswordMapper;
        this.icInterfaceInfoService = icInterfaceInfoService;
        this.environment = environment;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(ApiInvolvesaccountpasswordQueryCriteria criteria, Pageable pageable) {
        Page<ApiInvolvesaccountpassword> page = apiInvolvesaccountpasswordRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(apiInvolvesaccountpasswordMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<ApiInvolvesaccountpasswordDto> queryAll(ApiInvolvesaccountpasswordQueryCriteria criteria) {
        List<ApiInvolvesaccountpasswordDto> apiInvolvesaccountpasswordDtoList = apiInvolvesaccountpasswordMapper.toDto(apiInvolvesaccountpasswordRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
        return apiInvolvesaccountpasswordDtoList;
    }

    @Override
    //@Cacheable(key = "#p0")
    public ApiInvolvesaccountpasswordDto findById(Integer id) {
        ApiInvolvesaccountpassword apiInvolvesaccountpassword = apiInvolvesaccountpasswordRepository.findById(id).orElseGet(ApiInvolvesaccountpassword::new);
        ValidationUtil.isNull(apiInvolvesaccountpassword.getId(), "ApiInvolvesaccountpassword", "id", id);
        return apiInvolvesaccountpasswordMapper.toDto(apiInvolvesaccountpassword);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public ApiInvolvesaccountpasswordDto create(ApiInvolvesaccountpassword resources) {
        return apiInvolvesaccountpasswordMapper.toDto(apiInvolvesaccountpasswordRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(ApiInvolvesaccountpassword resources) {
        ApiInvolvesaccountpassword apiInvolvesaccountpassword = apiInvolvesaccountpasswordRepository.findById(resources.getId()).orElseGet(ApiInvolvesaccountpassword::new);
        ValidationUtil.isNull(apiInvolvesaccountpassword.getId(), "ApiInvolvesaccountpassword", "id", resources.getId());
        apiInvolvesaccountpassword.copy(resources);
        apiInvolvesaccountpasswordRepository.save(apiInvolvesaccountpassword);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            apiInvolvesaccountpasswordRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ApiInvolvesaccountpasswordDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        Integer size = 0;
        for (ApiInvolvesaccountpasswordDto apiInvolvesaccountpassword : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("接口编码", apiInvolvesaccountpassword.getApicode());
            map.put("接口URL", apiInvolvesaccountpassword.getApiurl());
            map.put("接口名称", apiInvolvesaccountpassword.getApiname());
            map.put("请求类型", apiInvolvesaccountpassword.getRequesttype());
            map.put("涉及账号密码", apiInvolvesaccountpassword.getInvolvesaccount());
            map.put("安全风险", RiskEnum.getRisk(apiInvolvesaccountpassword.getRisk()));
            map.put("检测时间", apiInvolvesaccountpassword.getChecktime());
            list.add(map);
            size = map.size();
        }
        FileUtil.downloadExcel(list, response, "接口请求内容检测结果", size);
    }

    @Override
    public List<ApiInvolvesaccountpassword> queryDescriptionInterfaceAlarmTypes(String beginDate, String endDate) {
        // TODO 根据配置执行对应SQL
        boolean isKingBase = false;
        String[] activeProfiles = environment.getActiveProfiles();
        // 使用 Arrays.asList 将数组转换为 List
        List<String> profileList = new ArrayList<>(Arrays.asList(activeProfiles));
        if (profileList.contains("kingbase")) {
            isKingBase = true;
        }

        if (isKingBase) {
            return apiInvolvesaccountpasswordRepository.queryDescriptionInterfaceAlarmTypesForKingBase(beginDate, endDate);
        } else {
            return apiInvolvesaccountpasswordRepository.queryDescriptionInterfaceAlarmTypes(beginDate, endDate);
        }
    }
}