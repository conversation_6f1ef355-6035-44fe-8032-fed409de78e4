package com.wzsec.modules.ic.service.impl;

import com.wzsec.modules.api.enums.SceneEnum;
import com.wzsec.modules.api.enums.StatusEnum;
import com.wzsec.modules.ic.domain.IcAlarmdisposal;
import com.wzsec.modules.ic.domain.enums.RiskEnum;
import com.wzsec.modules.ic.repository.IcAlarmdisposalRepository;
import com.wzsec.modules.ic.repository.SQLDatasourceRepository;
import com.wzsec.modules.ic.service.IcAlarmdisposalService;
import com.wzsec.modules.ic.service.IcInterfaceInfoService;
import com.wzsec.modules.ic.service.dto.IcAlarmdisposalDto;
import com.wzsec.modules.ic.service.dto.IcAlarmdisposalQueryCriteria;
import com.wzsec.modules.ic.service.mapper.IcAlarmdisposalMapper;
import com.wzsec.modules.monitor.config.MonitorRiskAlarmData;
import com.wzsec.modules.source.domain.Datasource;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

import static com.wzsec.utils.DateUtils.formatDateTime;

/**
 * <AUTHOR>
 * @date 2022-10-09
 */
@Service
@Slf4j
//@CacheConfig(cacheNames = "icAlarmdisposal")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class IcAlarmdisposalServiceImpl implements IcAlarmdisposalService {

    @Autowired
    private IcInterfaceInfoService icInterfaceInfoService;

    private final IcAlarmdisposalRepository icAlarmdisposalRepository;

    private final IcAlarmdisposalMapper icAlarmdisposalMapper;

    private final UserService userService;

    private final Environment environment;

    private final SQLDatasourceRepository sqlDatasourceRepository;

    public IcAlarmdisposalServiceImpl(IcAlarmdisposalRepository icAlarmdisposalRepository,
                                      IcAlarmdisposalMapper icAlarmdisposalMapper,
                                      UserService userService,
                                      Environment environment, SQLDatasourceRepository sqlDatasourceRepository) {
        this.icAlarmdisposalRepository = icAlarmdisposalRepository;
        this.icAlarmdisposalMapper = icAlarmdisposalMapper;
        this.userService = userService;
        this.environment = environment;
        this.sqlDatasourceRepository = sqlDatasourceRepository;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(IcAlarmdisposalQueryCriteria criteria, Pageable pageable) {

        String srcurl = "";
        String username = "";
        String password = "";
        //查询数据源信息
        List<Datasource> datasources = sqlDatasourceRepository.getDataSourceInformation(Const.ES_INTERFACEDATAQUERY);
        for (Datasource datasource : datasources) {
            srcurl = datasource.getSrcurl();  //数据源URL
            username = datasource.getUsername(); //用户名
            password = AES.decrypt(datasource.getPassword(), Const.AES_SECRET_KEY); //密码
        }

        Page<IcAlarmdisposal> page = icAlarmdisposalRepository.findAll((root, criteriaQuery, criteriaBuilder)
                -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);

        Page<IcAlarmdisposalDto> map = page.map(icAlarmdisposalMapper::toDto);

        // for (IcAlarmdisposalDto icInterfaceInfoDto : map) {
        //     String id = icInterfaceInfoDto.getReservefield2();
        //     if (id.length() < 100) {
        //         try {
        //             // 构建基础查询
        //             cn.hutool.json.JSONObject queryObject = new cn.hutool.json.JSONObject();
        //
        //             // 添加其他查询条件
        //             EsHttpQuery.addMatchQueryCondition(queryObject, "_id", id); // ES.id 唯一标识
        //
        //             String responseResults = EsHttpQuery.conditionalJsonQuery("netflow-*", queryObject.toString(), username, password, srcurl);
        //
        //             // 解析 JSON
        //             JSONObject jsonObject = JSON.parseObject(responseResults);
        //
        //             // 提取 "hits" 字段的内容
        //             JSONObject hitsObject = jsonObject.getJSONObject("hits");
        //
        //             // 提取 "hits" 数组
        //             JSONArray hitsArray = hitsObject.getJSONArray("hits");
        //
        //             if (hitsArray.size() > 0) {
        //                 // 获取数组元素
        //                 JSONObject hitObject = hitsArray.getJSONObject(0);
        //                 // 提取 "_source" 字段
        //                 JSONObject sourceObject = hitObject.getJSONObject("_source");
        //                 icInterfaceInfoDto.setReservefield2(sourceObject.toString());
        //             }
        //
        //         } catch (Exception e) {
        //         }
        //     }
        // }

        return PageUtil.toPage(map);
    }

    @Override
    //@Cacheable
    public List<IcAlarmdisposalDto> queryAll(IcAlarmdisposalQueryCriteria criteria) {
        return icAlarmdisposalMapper.toDto(icAlarmdisposalRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    //@Cacheable(key = "#p0")
    public IcAlarmdisposalDto findById(Integer id) {
        IcAlarmdisposal icAlarmdisposal = icAlarmdisposalRepository.findById(id).orElseGet(IcAlarmdisposal::new);
        ValidationUtil.isNull(icAlarmdisposal.getId(), "IcAlarmdisposal", "id", id);
        return icAlarmdisposalMapper.toDto(icAlarmdisposal);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public IcAlarmdisposalDto create(IcAlarmdisposal resources) {
        return icAlarmdisposalMapper.toDto(icAlarmdisposalRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(IcAlarmdisposal resources) {
        // TODO 根据配置执行对应SQL
        Boolean isKingBase = false;
        String[] activeProfiles = environment.getActiveProfiles();
        // 使用 Arrays.asList 将数组转换为 List
        List<String> profileList = new ArrayList<>(Arrays.asList(activeProfiles));
        if (profileList.contains("kingbase")) {
            isKingBase = true;
        }

        IcAlarmdisposal icAlarmdisposal = icAlarmdisposalRepository.findById(resources.getId()).orElseGet(IcAlarmdisposal::new);
        ValidationUtil.isNull(icAlarmdisposal.getId(), "IcAlarmdisposal", "id", resources.getId());
        icAlarmdisposal.copy(resources);
        icAlarmdisposalRepository.save(icAlarmdisposal);
        //更新推送次数
        int pushnumber = 0;
        String pushNum = "0";
        if (isKingBase) {
            pushNum = icAlarmdisposalRepository.queryPushTimesForKingBase(resources.getId());
        } else {
            pushNum = icAlarmdisposalRepository.queryPushTimes(resources.getId());
        }

        if (StringUtils.isNotBlank(pushNum)) {
            pushnumber = Integer.parseInt(pushNum);
        }
        pushnumber++;
        icAlarmdisposalRepository.updatePushTimes(resources.getId(), pushnumber, userService.findCurrentUserName(), formatDateTime(new Date()));
        try {
            //事件处置推送 syslog
            new MonitorRiskAlarmData(userService).sendIcExample(resources);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            icAlarmdisposalRepository.deleteById(id);
        }
    }

    @Override
    public String download(List<IcAlarmdisposalDto> all) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (IcAlarmdisposalDto icAlarmdisposal : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("接口编码", icAlarmdisposal.getApicode());
            map.put("接口名称", icAlarmdisposal.getApiname());
            map.put("检测模型", SceneEnum.getScene(icAlarmdisposal.getDetectionmodel()));
            map.put("事件详情", icAlarmdisposal.getCircumstantiality());
            map.put("接口日志", icAlarmdisposal.getReservefield2());
            map.put("风险程度", icAlarmdisposal.getRisk());
            map.put("调整级别", icAlarmdisposal.getReservefield1());
            map.put("处理状态", StatusEnum.getStatus(icAlarmdisposal.getTreatmentstate()));
            map.put("告警时间", icAlarmdisposal.getChecktime());
            map.put("处置人", icAlarmdisposal.getReservefield5());
            map.put("处置时间", icAlarmdisposal.getReservefield6());
            map.put("处置描述", icAlarmdisposal.getNote());
            map.put("推送次数", icAlarmdisposal.getPushnumber());
            list.add(map);
        }
        return FileUtil.downloadExcel(list);
    }

    @Override
    public String downloadMailExportExcel(List<IcAlarmdisposalDto> all) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (IcAlarmdisposalDto icAlarmdisposal : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("接口编码", icAlarmdisposal.getApicode());
            map.put("接口名称", icAlarmdisposal.getApiname());
            map.put("检测模型", SceneEnum.getScene(icAlarmdisposal.getDetectionmodel()));
            map.put("事件详情", icAlarmdisposal.getCircumstantiality());
            map.put("接口日志", icAlarmdisposal.getReservefield2());
            map.put("风险程度", icAlarmdisposal.getRisk());
            map.put("调整级别", icAlarmdisposal.getReservefield1());
            map.put("处理状态", StatusEnum.getStatus(icAlarmdisposal.getTreatmentstate()));
            map.put("告警时间", icAlarmdisposal.getChecktime());
            map.put("处置人", icAlarmdisposal.getReservefield5());
            map.put("处置时间", icAlarmdisposal.getReservefield6());
            map.put("处置描述", icAlarmdisposal.getNote());
            map.put("推送次数", icAlarmdisposal.getPushnumber());
            list.add(map);
        }
        return FileUtil.mailExportExcel(list);
    }

    @Override
    public void download(List<IcAlarmdisposalDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (IcAlarmdisposalDto icAlarmdisposal : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("请求方标识", icAlarmdisposal.getAk());
            map.put("接口编码", icAlarmdisposal.getApicode());
            map.put("请求方应用名称", icAlarmdisposal.getReqapp());
            map.put("请求方部门", icAlarmdisposal.getReqdepartment());
            map.put("请求IP", icAlarmdisposal.getReqdepartment());
            map.put("接口名称", icAlarmdisposal.getApiname());
            map.put("接口服务IP", icAlarmdisposal.getDestinationip());
            map.put("接口服务系统", icAlarmdisposal.getIcressystem());
            map.put("接口服务方部门", icAlarmdisposal.getIcresdepartment());
            map.put("接口URL", icAlarmdisposal.getApiurl());
            map.put("检测模型", SceneEnum.getScene(icAlarmdisposal.getDetectionmodel()));
            map.put("事件详情", icAlarmdisposal.getCircumstantiality());
            map.put("风险程度", RiskEnum.getRisk(icAlarmdisposal.getRisk()));
            map.put("调整级别", RiskEnum.getRisk(icAlarmdisposal.getReservefield1()));
            map.put("处理状态", StatusEnum.getStatus(icAlarmdisposal.getTreatmentstate()));
            map.put("告警时间", icAlarmdisposal.getChecktime());
            map.put("处置人", icAlarmdisposal.getReservefield5());
            map.put("处置时间", icAlarmdisposal.getReservefield6());
            map.put("处置描述", icAlarmdisposal.getNote());
            map.put("推送次数", icAlarmdisposal.getPushnumber());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }


    @Override
    public Integer queryQuantityByApiCodeAndModel(String apiCode, String detectionModel) {
        return icAlarmdisposalRepository.queryQuantityByApiCodeAndModel(apiCode, detectionModel);
    }

    @Override
    public void complianceAuditRiskModification(String risk, String apiCode, String detectionModel) {
        icAlarmdisposalRepository.complianceAuditRiskModification(risk, apiCode, detectionModel);
    }

    @Override
    public Integer queryAlarmAmount(String beginDate, String endDate, String risk) {
        return icAlarmdisposalRepository.queryAlarmAmount(beginDate, endDate, risk);
    }

    @Override
    public Integer scenarioAlarmAmount(String beginDate, String endDate, String detectionModel) {
        return icAlarmdisposalRepository.scenarioAlarmAmount(beginDate, endDate, detectionModel);
    }

    @Override
    public Integer scenarioAlarmAmount(String beginDate, String endDate, String detectionModel, List<String> domainApiCodeList) {
        return icAlarmdisposalRepository.scenarioAlarmAmount(beginDate, endDate, detectionModel, domainApiCodeList);
    }

    @Override
    public Integer queryAlarmDisposalNum(String beginDate, String endDate, String treatmentState) {
        return icAlarmdisposalRepository.queryAlarmDisposalNum(beginDate, endDate, treatmentState);
    }

    @Override
    public Integer queryTotalNumberAlarms(String beginDate, String endDate) {
        return icAlarmdisposalRepository.queryTotalNumberAlarms(beginDate, endDate);
    }

    @Override
    public Integer queryCumulativeDateInterface() {
        return icAlarmdisposalRepository.queryCumulativeDateInterface();
    }

    @Override
    public Integer queryRiskAlarmAmount(String beginDate, String endDate, String risk) {
        return icAlarmdisposalRepository.queryRiskAlarmAmount(beginDate, endDate, risk);
    }

    @Override
    public Integer queryRiskAlarmDisposalNum(String beginDate, String endDate, String treatmentState) {
        return icAlarmdisposalRepository.queryRiskAlarmDisposalNum(beginDate, endDate, treatmentState);
    }

    @Override
    public Integer queryRiskAlarmDisposalNum(String treatmentState) {
        return icAlarmdisposalRepository.queryRiskAlarmDisposalNum(treatmentState);
    }

    @Override
    public Integer queryRiskAlarmDisposalNum(String beginDate, String endDate, String treatmentState, String risk) {
        return icAlarmdisposalRepository.queryRiskAlarmDisposalNum(beginDate, endDate, treatmentState, risk);
    }

    @Override
    public List<Map<String, Object>> queryInterfaceAlarmsNum(String beginDate, String endDate) {
        return icAlarmdisposalRepository.queryInterfaceAlarmsNum(beginDate, endDate);
    }

    @Override
    public List<Map<String, Object>> queryInterfaceAlarmCounts() {
        return icAlarmdisposalRepository.queryInterfaceAlarmCounts();
    }

    @Override
    public void batchPush(Integer id) {
        // TODO 根据配置执行对应SQL
        Boolean isKingBase = false;
        String[] activeProfiles = environment.getActiveProfiles();
        // 使用 Arrays.asList 将数组转换为 List
        List<String> profileList = new ArrayList<>(Arrays.asList(activeProfiles));
        if (profileList.contains("kingbase")) {
            isKingBase = true;
        }

        try {
            //更新推送次数
            int pushnumber = 0;
            String pushNum = "0";
            if (isKingBase) {
                pushNum = icAlarmdisposalRepository.queryPushTimesForKingBase(id);
            } else {
                pushNum = icAlarmdisposalRepository.queryPushTimes(id);
            }
            if (StringUtils.isNotBlank(pushNum)) {
                pushnumber = Integer.parseInt(pushNum);
            }
            pushnumber++;
            icAlarmdisposalRepository.updatePushTimes(id, pushnumber, userService.findCurrentUserName(), formatDateTime(new Date()));
            //事件处置批量推送 syslog
            new MonitorRiskAlarmData(userService).sendIcExample(icAlarmdisposalRepository.obtainBatchPushData(id));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void updatealarmapistate(String eventId, String state) {
        icAlarmdisposalRepository.updatealarmapistate(eventId, state);
    }

    @Override
    public void batchPush(String id, String risk, String treatmentstate, String note) {
        // TODO 根据配置执行对应SQL
        Boolean isKingBase = false;
        String[] activeProfiles = environment.getActiveProfiles();
        // 使用 Arrays.asList 将数组转换为 List
        List<String> profileList = new ArrayList<>(Arrays.asList(activeProfiles));
        if (profileList.contains("kingbase")) {
            isKingBase = true;
        }
        try {
            //更新推送次数
            int pushnumber = 0;
            String pushNum = "0";
            if (isKingBase) {
                pushNum = icAlarmdisposalRepository.queryPushTimesForKingBase(Integer.valueOf(id));
            } else {
                pushNum = icAlarmdisposalRepository.queryPushTimes(Integer.valueOf(id));
            }
            if (StringUtils.isNotBlank(pushNum)) {
                pushnumber = Integer.parseInt(pushNum);
            }
            pushnumber++;
            icAlarmdisposalRepository.updatePushTimes(Integer.valueOf(id), pushnumber, userService.findCurrentUserName(),
                    formatDateTime(new Date()), risk, treatmentstate, note);
            //事件处置批量推送 syslog
            new MonitorRiskAlarmData(userService).sendIcExample(icAlarmdisposalRepository.obtainBatchPushData(Integer.valueOf(id)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<Map<String, Object>> scenarioAlarmAmountByData(String beginDate, String endDate) {
        return icAlarmdisposalRepository.scenarioAlarmAmountByData(beginDate, endDate);
    }

    @Override
    public List<Map<String, Object>> scenarioAlarmAmountByData(String beginDate, String endDate, List<String> domainApiCodeList) {
        return icAlarmdisposalRepository.scenarioAlarmAmountByData(beginDate, endDate, domainApiCodeList);
    }


    @Override
    public List<Map<String, Object>> queryRiskAlarm(String beginDate, String endDate, String risk) {
        return icAlarmdisposalRepository.queryRiskAlarm(beginDate, endDate, risk);
    }

    @Override
    public List<Map<String, Object>> queryAlarmAmount(String beginDate, String endDate) {
        return icAlarmdisposalRepository.queryAlarmAmount(beginDate, endDate);
    }

    @Override
    public List<Map<String, Object>> queryAlarmAmount(String beginDate, String endDate, List<String> domainApiCodeList) {
        return icAlarmdisposalRepository.queryAlarmAmount(beginDate, endDate, domainApiCodeList);
    }

    @Override
    public List<String> queryingAlarmDetails(String beginDate, String endDate) {
        return icAlarmdisposalRepository.queryingAlarmDetails(beginDate, endDate);
    }

    @Override
    public List<String> queryingAlarmDetails(String beginDate, String endDate, List<String> domainApiCodeList) {
        return icAlarmdisposalRepository.queryingAlarmDetails(beginDate, endDate, domainApiCodeList);
    }

    @Override
    public List<Map<String, Object>> queryAlarmDisposalNum(String beginDate, String endDate) {
        return icAlarmdisposalRepository.queryAlarmDisposalNum(beginDate, endDate);
    }

    @Override
    public List<Map<String, Object>> queryAlarmDisposalNum(String beginDate, String endDate, List<String> domainApiCodeList) {
        return icAlarmdisposalRepository.queryAlarmDisposalNum(beginDate, endDate, domainApiCodeList);
    }
}