package com.wzsec.modules.ic.service.impl;

import com.wzsec.modules.ic.domain.IcInterfaceInfo;
import com.wzsec.modules.ic.domain.IcInvokingRecord;
import com.wzsec.modules.ic.domain.enums.RiskEnum;
import com.wzsec.modules.ic.repository.IcInvokingRecordRepository;
import com.wzsec.modules.ic.service.IcInterfaceInfoService;
import com.wzsec.modules.ic.service.IcInvokingRecordService;
import com.wzsec.modules.ic.service.dto.IcInvokingRecordDto;
import com.wzsec.modules.ic.service.dto.IcInvokingRecordQueryCriteria;
import com.wzsec.modules.ic.service.mapper.IcInvokingRecordMapper;
import com.wzsec.modules.system.service.DictDetailService;
import com.wzsec.utils.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2021-04-25
 */
@Service
//@CacheConfig(cacheNames = "icInvokingrecord")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class IcInvokingRecordServiceImpl implements IcInvokingRecordService {

    private final IcInvokingRecordRepository icInvokingrecordRepository;

    private final IcInvokingRecordMapper icInvokingrecordMapper;

    private final IcInterfaceInfoService icInterfaceInfoService;

    private final DictDetailService dictDetailService;

    public IcInvokingRecordServiceImpl(IcInvokingRecordRepository icInvokingrecordRepository,
                                       IcInvokingRecordMapper icInvokingrecordMapper,
                                       IcInterfaceInfoService icInterfaceInfoService,
                                       DictDetailService dictDetailService) {
        this.icInvokingrecordRepository = icInvokingrecordRepository;
        this.icInvokingrecordMapper = icInvokingrecordMapper;
        this.icInterfaceInfoService = icInterfaceInfoService;
        this.dictDetailService = dictDetailService;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(IcInvokingRecordQueryCriteria criteria, Pageable pageable) {
        Map<String, IcInterfaceInfo> fullInterface = icInterfaceInfoService.findFullInterface();
        Page<IcInvokingRecord> page = icInvokingrecordRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(icInvokingrecordMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<IcInvokingRecordDto> queryAll(IcInvokingRecordQueryCriteria criteria) {
        Map<String, IcInterfaceInfo> fullInterface = icInterfaceInfoService.findFullInterface();
        List<IcInvokingRecordDto> dto = icInvokingrecordMapper.toDto(icInvokingrecordRepository.
                findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
        return dto;
    }

    @Override
    //@Cacheable(key = "#p0")
    public IcInvokingRecordDto findById(Integer id) {
        IcInvokingRecord icInvokingrecord = icInvokingrecordRepository.findById(id).orElseGet(IcInvokingRecord::new);
        ValidationUtil.isNull(icInvokingrecord.getId(), "IcInvokingRecord", "id", id);
        return icInvokingrecordMapper.toDto(icInvokingrecord);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public IcInvokingRecordDto create(IcInvokingRecord resources) {
        return icInvokingrecordMapper.toDto(icInvokingrecordRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(IcInvokingRecord resources) {
        IcInvokingRecord icInvokingrecord = icInvokingrecordRepository.findById(resources.getId()).orElseGet(IcInvokingRecord::new);
        ValidationUtil.isNull(icInvokingrecord.getId(), "IcInvokingRecord", "id", resources.getId());
        icInvokingrecord.copy(resources);
        icInvokingrecordRepository.save(icInvokingrecord);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            icInvokingrecordRepository.deleteById(id);
        }
    }


    @Override
    public void download(List<IcInvokingRecordDto> all, HttpServletResponse response) throws IOException {
        Map<String, String> systemNumberMap = dictDetailService.getDictDetailMap("system_number");
        List<Map<String, Object>> list = new ArrayList<>();
        Integer size = 0;
        for (IcInvokingRecordDto icInvokingrecord : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("调用方编码", icInvokingrecord.getSparefield1());
            map.put("接口编码", icInvokingrecord.getApicode());
            map.put("接口名称", icInvokingrecord.getSparefield3());
            map.put("当日调用数量", icInvokingrecord.getNum());
            map.put("七日平均调用量", icInvokingrecord.getAvgcall());
            map.put("风险程度", RiskEnum.getRisk(icInvokingrecord.getRisk()));
            map.put("调用日期", icInvokingrecord.getDate());
            map.put("系统标识", systemNumberMap.getOrDefault(icInvokingrecord.getSystemname(), ""));
            map.put("检测时间", icInvokingrecord.getInsertdatetime());
            list.add(map);
            size = map.size();
        }
        FileUtil.downloadExcel(list, response, "API调用量检测结果", size);
    }


    /**
     * 查询七日平均统计告警
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @throws IOException /
     */
    //@Override
    public List<IcInvokingRecordDto> getInvokingRecordWarning(String startDate, String endDate, String isWarning) {
        List<IcInvokingRecord> invokingRecordWarningList = icInvokingrecordRepository
                .getInvokingRecordWarning(startDate, endDate);
        for (IcInvokingRecord icInvokingRecord : invokingRecordWarningList) {
            Long avgnum = icInvokingRecord.getAvgcall(); //七日平均调用量
            Long num = icInvokingRecord.getNum(); //当日调用量
            //告警级别判定
            if (avgnum != 0) {
                Long i = num / avgnum;
                if (2 < i && i <= 5) {
                    icInvokingRecord.setRisk(Const.RISK_LOW);//低风险
                } else if (5 < i && i <= 10) {
                    icInvokingRecord.setRisk(Const.RISK_MIDDLE);//中风险
                } else if (i > 10) {
                    icInvokingRecord.setRisk(Const.RISK_HIGH);//高风险
                } else {
                    icInvokingRecord.setRisk(Const.RISK_NOT);//无风险
                }
            } else {
                if (1000 < num && num <= 10000) {
                    icInvokingRecord.setRisk(Const.RISK_LOW);//低风险
                } else if (10000 < num && num <= 20000) {
                    icInvokingRecord.setRisk(Const.RISK_MIDDLE);//中风险
                } else if (num > 20000) {
                    icInvokingRecord.setRisk(Const.RISK_HIGH);//高风险
                } else {
                    icInvokingRecord.setRisk(Const.RISK_NOT);//无风险
                }
            }
        }

        return icInvokingrecordMapper.toDto(invokingRecordWarningList);
    }


    @Override
    public List<IcInvokingRecord> queryContentResult(String beginDate, String endDate) {
        return icInvokingrecordRepository.queryContentResult(beginDate, endDate);
    }

    @Override
    public IcInvokingRecord maximumNumberOfCalls(String beginDate, String endDate) {
        return icInvokingrecordRepository.maximumNumberOfCalls(beginDate, endDate);
    }

    @Override
    public IcInvokingRecord maximumNumberOfCalls(String beginDate, String endDate, List<String> domainApiCodeList) {
        return icInvokingrecordRepository.maximumNumberOfCalls(beginDate, endDate, domainApiCodeList);
    }

    @Override
    public int queryTotalLogs(String beginDate, String endDate) {
        return icInvokingrecordRepository.queryTotalLogs(beginDate, endDate);
    }

    @Override
    public int queryTotalLogs(String beginDate, String endDate, List<String> domainApiCodeList) {
        return icInvokingrecordRepository.queryTotalLogs(beginDate, endDate, domainApiCodeList);
    }

    @Override
    public int queryTotalApiCode(String beginDate, String endDate) {
        return icInvokingrecordRepository.queryTotalApiCode(beginDate, endDate);
    }

    @Override
    public int queryTotalApiCode(String beginDate, String endDate, List<String> domainApiCodeList) {
        return icInvokingrecordRepository.queryTotalApiCode(beginDate, endDate, domainApiCodeList);
    }

    @Override
    public List<String> querySevenDayMeanValueTOP(String beginDate, String endDate) {
        return icInvokingrecordRepository.querySevenDayMeanValueTOP(beginDate, endDate);
    }

    @Override
    public String queryDailyCallSituation(String theSameDay, String apiCode) {
        return icInvokingrecordRepository.queryDailyCallSituation(theSameDay, apiCode);
    }

    @Override
    public List<Map<String, Object>> queryDailyCall(String beginDate, String endDate, String apicode) {
        return icInvokingrecordRepository.queryDailyCall(beginDate, endDate, apicode);
    }

}
