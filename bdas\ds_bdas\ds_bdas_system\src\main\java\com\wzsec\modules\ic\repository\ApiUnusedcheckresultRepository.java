package com.wzsec.modules.ic.repository;

import com.wzsec.modules.ic.domain.ApiUnusedcheckresult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
public interface ApiUnusedcheckresultRepository extends JpaRepository<ApiUnusedcheckresult, Integer>, JpaSpecificationExecutor<ApiUnusedcheckresult> {

    /**
     * 查询最新时间
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link String}
     */
    @Query(value = "SELECT MAX(checktime) FROM sdd_api_unusedcheckresult WHERE checktime BETWEEN :beginDate AND :endDate ", nativeQuery = true)
    String queryLatestTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate);


    /**
     * 查询最新时间
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link String}
     */
    @Query(value = "SELECT MAX(checktime) FROM sdd_api_unusedcheckresult WHERE checktime BETWEEN :beginDate AND :endDate AND apicode IN (:domainApiCodeList) ", nativeQuery = true)
    String queryLatestTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);


    /**
     * 查询最新时间
     *
     * @return {@link String}
     */
    @Query(value = "SELECT MAX(checktime) FROM sdd_api_unusedcheckresult ", nativeQuery = true)
    String queryLatestTime();

    /**
     * 查询最近不常调用接口
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    @Query(value = "SELECT DISTINCT(apicode) FROM sdd_api_unusedcheckresult WHERE checktime BETWEEN :beginDate AND :endDate", nativeQuery = true)
    List<String> queryLatestUnusedCheckResults(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 查询最近不常调用接口
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    @Query(value = "SELECT * FROM sdd_api_unusedcheckresult WHERE checktime BETWEEN :beginDate AND :endDate  ORDER BY checktime, risk DESC", nativeQuery = true)
    List<ApiUnusedcheckresult> getinaliveapi(@Param("beginDate") String beginDate, @Param("endDate") String endDate);


    /**
     * 查询最近不常调用接口
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    @Query(value = "SELECT * FROM sdd_api_unusedcheckresult WHERE checktime BETWEEN :beginDate AND :endDate ORDER BY checktime, risk DESC", nativeQuery = true)
    List<ApiUnusedcheckresult> queryLatestUnusedCheckResult(@Param("beginDate") String beginDate, @Param("endDate") String endDate);


    /**
     * 查询最近不常调用接口
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    @Query(value = "SELECT * FROM sdd_api_unusedcheckresult WHERE checktime BETWEEN :beginDate AND :endDate AND apicode IN (:domainApiCodeList) ORDER BY checktime, risk DESC", nativeQuery = true)
    List<ApiUnusedcheckresult> queryLatestUnusedCheckResult(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);


    /**
     * 查询数量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    @Query(value = "SELECT COUNT(DISTINCT apicode) FROM sdd_api_unusedcheckresult WHERE checktime BETWEEN :beginDate AND :endDate ", nativeQuery = true)
    int queryQuantity(@Param("beginDate") String beginDate, @Param("endDate") String endDate);
}