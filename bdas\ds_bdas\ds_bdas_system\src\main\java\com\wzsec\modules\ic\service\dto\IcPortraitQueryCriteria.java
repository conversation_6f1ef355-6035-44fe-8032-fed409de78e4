package com.wzsec.modules.ic.service.dto;

import lombok.Data;
import java.util.List;
import com.wzsec.annotation.Query;

/**
* <AUTHOR>
* @date 2022-10-19
*/
@Data
public class IcPortraitQueryCriteria{

    /** 支持精确查询和IN查询：单个值时精确查询，逗号分隔字符串或List时IN查询 */
    @Query(type = Query.Type.IN)
    private Object apicode;

    @Query
    private String safetyindex;

    @Query
    private String organization;

    @Query
    private String registerUnit;

    @Query(blurry = "apicode,apiname")
    private String blurry;
}
