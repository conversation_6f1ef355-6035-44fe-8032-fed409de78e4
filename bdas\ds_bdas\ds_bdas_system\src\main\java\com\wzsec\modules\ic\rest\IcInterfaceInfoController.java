package com.wzsec.modules.ic.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.IcInterfaceInfo;
import com.wzsec.modules.ic.service.ApidiscoveryService;
import com.wzsec.modules.ic.service.IcInterfaceInfoService;
import com.wzsec.modules.ic.service.dto.IcInterfaceInfoQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-15
 */
// @Api(tags = "接口日志审计管理")
@RestController
@RequestMapping("/api/icInterfaceInfo")
public class IcInterfaceInfoController {

    private final IcInterfaceInfoService icInterfaceInfoService;

    private final ApidiscoveryService apidiscoveryService;

    public IcInterfaceInfoController(IcInterfaceInfoService icInterfaceInfoService, ApidiscoveryService apidiscoveryService) {
        this.icInterfaceInfoService = icInterfaceInfoService;
        this.apidiscoveryService = apidiscoveryService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    // @PreAuthorize("@el.check('icInterfaceinfo:download')")
    public void download(HttpServletResponse response, IcInterfaceInfoQueryCriteria criteria) throws IOException {
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        icInterfaceInfoService.download(icInterfaceInfoService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口日志审计")
    // @ApiOperation("查询接口日志审计")
    // @PreAuthorize("@el.check('icInterfaceInfo:list')")
    public ResponseEntity<Object> getIcInterfaceinfos(IcInterfaceInfoQueryCriteria criteria, Pageable pageable) {
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        return new ResponseEntity<>(icInterfaceInfoService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口日志审计")
    // @ApiOperation("新增接口日志审计")
    // @PreAuthorize("@el.check('icInterfaceInfo:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody IcInterfaceInfo resources) {
//        int count = icInterfaceInfoService.findTheNumberOfCustname(resources.getCustname());
//        if (count > 0) {
//            throw new BadRequestException("客户名称不能重复");
//        }
        return new ResponseEntity<>(icInterfaceInfoService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口日志审计")
    // @ApiOperation("修改接口日志审计")
    // @PreAuthorize("@el.check('icInterfaceInfo:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody IcInterfaceInfo resources) {
//        int count = icInterfaceInfoService.findTheNumberOfCustname(resources.getCustname());
//        if (count > 0) {
//            if (icInterfaceInfoService.findId(resources.getCustname()) != resources.getId()) {
//                throw new BadRequestException("客户名称不能重复");
//            }
//        }
        icInterfaceInfoService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口日志审计")
    // @ApiOperation("删除接口日志审计")
    // @PreAuthorize("@el.check('icInterfaceInfo:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        icInterfaceInfoService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/getParamByTypeAndApicode/{type}/{apicode}")
    @Log("根据apicode查询接口参数信息")
    // @ApiOperation("根据apicode查询接口参数信息")
    public ResponseEntity<Object> getParamByTypeAndApicode(@PathVariable String type, @PathVariable String apicode) {
        return new ResponseEntity<>(icInterfaceInfoService.getParamByTypeAndApicode(type, apicode), HttpStatus.OK);
    }

//    @GetMapping("/getAllApicode")
//    @Log("查询所以接口编码")
//    // @ApiOperation("查询所以接口编码")
//    public ResponseEntity<Object> getAllApicode() {
//        return new ResponseEntity<>(icInterfaceInfoService.getAllApicode(), HttpStatus.OK);
//    }

    @GetMapping("/getAllApicode")
    @Log("查询所以接口编码")
    // @ApiOperation("查询所以接口编码")
    public ResponseEntity<Object> getAllApicode() {
        return new ResponseEntity<>(apidiscoveryService.queryApicodeList(), HttpStatus.OK);
    }
}
