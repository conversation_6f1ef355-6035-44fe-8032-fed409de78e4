package com.wzsec.modules.ic.service;

import com.wzsec.modules.ic.domain.ApiInfrequentlycall;
import com.wzsec.modules.ic.service.dto.ApiInfrequentlycallDto;
import com.wzsec.modules.ic.service.dto.ApiInfrequentlycallQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
public interface ApiInfrequentlycallService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(ApiInfrequentlycallQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<ApiInfrequentlycallDto>
     */
    List<ApiInfrequentlycallDto> queryAll(ApiInfrequentlycallQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return ApiInfrequentlycallDto
     */
    ApiInfrequentlycallDto findById(Integer id);

    /**
     * 创建
     *
     * @param resources /
     * @return ApiInfrequentlycallDto
     */
    ApiInfrequentlycallDto create(ApiInfrequentlycall resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(ApiInfrequentlycall resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Integer[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<ApiInfrequentlycallDto> all, HttpServletResponse response) throws IOException;


    /**
     * 查询内容结果(word导出取五条)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiInfrequentlycall}>
     */
    List<ApiInfrequentlycall> queryContentResult(String beginDate, String endDate);

    /**
     * 查询最新时间
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link String}
     */
    String queryLatestTime(String beginDate, String endDate);

    /**
     * 查询最新时间
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link String}
     */
    String queryLatestTime(String beginDate, String endDate, List<String> domainApiCodeList);

    /**
     * 查询最近不常调用接口
     *
     * @param beginDate    开始日期
     * @param endDate      结束日期
     * @return {@link List}<{@link ApiInfrequentlycall}>
     */
    List<String> queryLatestInfrequentlyCall(String beginDate, String endDate);


    /**
     * 查询最近不常调用接口
     *
     * @param beginDate    开始日期
     * @param endDate      结束日期
     * @return {@link List}<{@link ApiInfrequentlycall}>
     */
    List<String> queryLatestInfrequentlyCall(String beginDate, String endDate, List<String> domainApiCodeList);


}