package com.wzsec.modules.ic.service;

import com.wzsec.modules.ic.domain.IcInvokingRecord;
import com.wzsec.modules.ic.service.dto.IcInvokingRecordDto;
import com.wzsec.modules.ic.service.dto.IcInvokingRecordQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-04-25
 */
public interface IcInvokingRecordService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(IcInvokingRecordQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<IcInvokingRecordDto>
     */
    List<IcInvokingRecordDto> queryAll(IcInvokingRecordQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return IcInvokingRecordDto
     */
    IcInvokingRecordDto findById(Integer id);

    /**
     * 创建
     *
     * @param resources /
     * @return IcInvokingRecordDto
     */
    IcInvokingRecordDto create(IcInvokingRecord resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(IcInvokingRecord resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Integer[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<IcInvokingRecordDto> all, HttpServletResponse response) throws IOException;

    /**
     * 查询内容结果(word导出取五条)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link IcInvokingRecord}>
     */
    List<IcInvokingRecord> queryContentResult(String beginDate, String endDate);

    /**
     * 查询调用量最大对象
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link IcInvokingRecord}
     */
    IcInvokingRecord maximumNumberOfCalls(String beginDate, String endDate);

    /**
     * 查询调用量最大对象
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link IcInvokingRecord}
     */
    IcInvokingRecord maximumNumberOfCalls(String beginDate, String endDate, List<String> domainApiCodeList);

    /**
     * 日志调用总量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    int queryTotalLogs(String beginDate, String endDate);

    /**
     * 日志调用总量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    int queryTotalLogs(String beginDate, String endDate, List<String> domainApiCodeList);

    /**
     * 接口编码调用量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    int queryTotalApiCode(String beginDate, String endDate);

    /**
     * 接口编码调用量
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return int
     */
    int queryTotalApiCode(String beginDate, String endDate, List<String> domainApiCodeList);

    /**
     * 查询7天平均值TOP
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link String}>
     */
    List<String> querySevenDayMeanValueTOP(String beginDate, String endDate);

    /**
     * 查询每日接口调用情况
     *
     * @param apiCode apiCode
     * @return {@link IcInvokingRecord}
     */
    String queryDailyCallSituation(String theSameDay, String apiCode);


    /**
     * 查询每日接口调用情况
     *
     * @return {@link IcInvokingRecord}
     */
    List<Map<String, Object>> queryDailyCall(String beginDate, String endDate, String apicode);


}
