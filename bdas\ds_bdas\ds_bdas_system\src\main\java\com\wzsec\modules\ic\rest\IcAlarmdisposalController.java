package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.exception.BadRequestException;
import com.wzsec.modules.ic.domain.IcAlarmdisposal;
import com.wzsec.modules.ic.service.IcAlarmdisposalService;
import com.wzsec.modules.ic.service.dto.IcAlarmdisposalQueryCriteria;
import com.wzsec.utils.DomainUtil;
import com.wzsec.utils.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-09
 */
// @Api(tags = "接口告警处置管理")
@RestController
@RequestMapping("/api/icAlarmdisposal")
public class IcAlarmdisposalController {

    private final IcAlarmdisposalService icAlarmdisposalService;

    public IcAlarmdisposalController(IcAlarmdisposalService icAlarmdisposalService) {
        this.icAlarmdisposalService = icAlarmdisposalService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    // @PreAuthorize("@el.check('icAlarmdisposal:list')")
    public void download(HttpServletResponse response, IcAlarmdisposalQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        icAlarmdisposalService.download(icAlarmdisposalService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口告警处置")
    public ResponseEntity<Object> getIcAlarmdisposals(IcAlarmdisposalQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        return new ResponseEntity<>(icAlarmdisposalService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口告警处置")
    // @ApiOperation("新增接口告警处置")
    // @PreAuthorize("@el.check('icAlarmdisposal:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody IcAlarmdisposal resources) {
        return new ResponseEntity<>(icAlarmdisposalService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口告警处置")
    // @ApiOperation("修改接口告警处置")
    // @PreAuthorize("@el.check('icAlarmdisposal:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody IcAlarmdisposal resources) {
        icAlarmdisposalService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口告警处置")
    // @ApiOperation("删除接口告警处置")
    // @PreAuthorize("@el.check('icAlarmdisposal:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        icAlarmdisposalService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("批量开始推送")
    @PutMapping(value = "/batchPush")
    public void batchStartEvaluation(@RequestBody Integer[] ids) {
        for (Integer id : ids) {
            icAlarmdisposalService.batchPush(id);
        }
    }

    @Log("批量推送")
    @GetMapping(value = "/bulkPush")
    public void batchStartEvaluation(HttpServletRequest request) {
        String risk = request.getParameter("reservefield1");
        String treatmentstate = request.getParameter("treatmentstate");
        String note = request.getParameter("note");
        String idstrs = request.getParameter("idstrs");
        if (StringUtils.isBlank(idstrs)) {
            throw new BadRequestException("请最少选择一条进行批量推送！");
        }
        if (StringUtils.isBlank(risk)) {
            throw new BadRequestException("调整级别不能为空！");
        }
        if (StringUtils.isBlank(treatmentstate)) {
            throw new BadRequestException("处置状态不能为空！");
        }
        String[] split = idstrs.split(",");
        for (String id : split) {
            if (StringUtils.isNotBlank(id)) {
                icAlarmdisposalService.batchPush(id, risk, treatmentstate, note);
            }
        }
    }

}
