package com.wzsec.modules.emailsend.service.impl;

import cn.hutool.extra.mail.Mail;
import cn.hutool.extra.mail.MailAccount;
import com.wzsec.exception.BadRequestException;
import com.wzsec.modules.emailsend.domain.EmailRecord;
import com.wzsec.modules.emailsend.domain.EmailSendConfig;
import com.wzsec.modules.emailsend.repository.EmailRecordRepository;
import com.wzsec.modules.emailsend.repository.EmailSendConfigRepository;
import com.wzsec.modules.emailsend.service.EmailSendConfigService;
import com.wzsec.modules.emailsend.service.dto.EmailSendConfigDto;
import com.wzsec.modules.emailsend.service.dto.EmailSendConfigQueryCriteria;
import com.wzsec.modules.emailsend.service.mapper.EmailSendConfigMapper;
import com.wzsec.modules.ic.service.IcAlarmdisposalService;
import com.wzsec.modules.ic.service.IcApiauditreportService;
import com.wzsec.modules.ic.service.dto.IcAlarmdisposalQueryCriteria;
import com.wzsec.modules.ic.service.dto.IcApiauditreportQueryCriteria;
import com.wzsec.modules.system.service.UserService;
import com.wzsec.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.activation.DataSource;
import javax.activation.FileDataSource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-11-06
 */
@Slf4j
@Service
//@CacheConfig(cacheNames = "emailSendConfig")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class EmailSendConfigServiceImpl implements EmailSendConfigService {

    private final EmailSendConfigRepository emailSendConfigRepository;
    private final EmailSendConfigMapper emailSendConfigMapper;
    private final IcApiauditreportService icApiauditreportService;
    private final IcAlarmdisposalService icAlarmdisposalService;
    private final EmailRecordRepository emailRecordRepository;
    private final UserService userService;

    public EmailSendConfigServiceImpl(EmailSendConfigRepository emailSendConfigRepository,
                                      EmailSendConfigMapper emailSendConfigMapper,
                                      IcApiauditreportService icApiauditreportService,
                                      IcAlarmdisposalService icAlarmdisposalService,
                                      EmailRecordRepository emailRecordRepository,
                                      UserService userService) {
        this.emailSendConfigRepository = emailSendConfigRepository;
        this.emailSendConfigMapper = emailSendConfigMapper;
        this.icApiauditreportService = icApiauditreportService;
        this.icAlarmdisposalService = icAlarmdisposalService;
        this.emailRecordRepository = emailRecordRepository;
        this.userService = userService;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(EmailSendConfigQueryCriteria criteria, Pageable pageable) {
        Page<EmailSendConfig> page = emailSendConfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(emailSendConfigMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<EmailSendConfigDto> queryAll(EmailSendConfigQueryCriteria criteria) {
        return emailSendConfigMapper.toDto(emailSendConfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    //@Cacheable(key = "#p0")
    public EmailSendConfigDto findById(Long id) {
        EmailSendConfig emailSendConfig = emailSendConfigRepository.findById(id).orElseGet(EmailSendConfig::new);
        ValidationUtil.isNull(emailSendConfig.getId(), "EmailSendConfig", "id", id);
        return emailSendConfigMapper.toDto(emailSendConfig);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public EmailSendConfigDto create(EmailSendConfig resources) {
        resources.setExecutestate(Const.DB_TASK_EXECUTESTATE_NOT_SUBMIT);
        if (StringUtils.isNotEmpty(resources.getPass())) {
            //AES加密
            try {
                String encrypt = AES.encrypt(resources.getPass(), Const.AES_SECRET_KEY);
                resources.setPass(encrypt);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return emailSendConfigMapper.toDto(emailSendConfigRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(EmailSendConfig resources) {
        EmailSendConfig emailSendConfig = emailSendConfigRepository.findById(resources.getId()).orElseGet(EmailSendConfig::new);
        ValidationUtil.isNull(emailSendConfig.getId(), "EmailSendConfig", "id", resources.getId());
        String originalPwd = emailSendConfig.getPass();
        emailSendConfig.copy(resources);

        if (resources.getSubmittype().equals(Const.TASK_SUBMITTYPE_HAND)) { //修改为手动执行,将定时任务时间置为空
            emailSendConfig.setChecktime("");
            emailSendConfig.setExecutestate(Const.DB_TASK_EXECUTESTATE_NOT_SUBMIT);
        }
        if (StringUtils.isNotEmpty(resources.getPass()) && !resources.getPass().equals(originalPwd)) {
            //AES加密
            try {
                String encrypt = AES.encrypt(resources.getPass(), Const.AES_SECRET_KEY);
                emailSendConfig.setPass(encrypt);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        emailSendConfigRepository.save(emailSendConfig);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Long[] ids) {
        for (Long id : ids) {
            emailSendConfigRepository.deleteById(id);
        }
    }

    @Override
    public List<EmailSendConfigDto> getAllJob() {
        EmailSendConfigQueryCriteria criteria = new EmailSendConfigQueryCriteria();
        criteria.setTaskstate(Const.TASK_STATE_USE);
        criteria.setSubmittype(Const.TASK_SUBMITTYPE_AUTO);
        return emailSendConfigMapper.toDto(emailSendConfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }


    @Override
    @Async
    public void executionEmailSend(Long id, String currentUserName) {
        EmailSendConfig resources = emailSendConfigRepository.findById(id).orElseGet(EmailSendConfig::new);
        ValidationUtil.isNull(resources.getId(), "EmailSendConfig", "id", id);
        try {

            if (resources.getTaskstate().equals(Const.TASK_STATE_USE)) {   //判断是否启用
                resources.setExecutestate(Const.TASK_EXECUTESTATE_EXECUTING);  // 执行中

                String jobReceiver = resources.getReceiver();//收件人
                String jobUser = resources.getUsers();//用户名
                String jobHost = resources.getHost();//SMTP地址
                String jobPort = resources.getPort();//SMTP端口
                String jobPass = resources.getPass();//SMTP密码
                String jobFromUser = resources.getFromUser();//发件人
                String jobChecktime = resources.getChecktime();//定时时间
                String jobMotif = resources.getMotif();//邮件主题

                List<String> list = new ArrayList<>();
                //判断收件人是否存在多个
                if (jobReceiver.contains(",")) {
                    list = Arrays.asList(jobReceiver.split(","));
                } else {
                    list.add(jobReceiver);
                }
                String tempPath = ""; //附件路径
                String content = ""; //内容
                String interfaceAlarm = "接口检测告警";

                //判断周报,日报
                if (Const.EMAIL_TITLEONE.equals(jobMotif)) {
                    //从配置文件获取审计报告时间
                    String beginDate = "";

                    String[] split = jobChecktime.split("\\s+");

                    String day = split[3].trim(); //日期
                    String month = split[4].trim(); //月份
                    String week = split[5].trim(); //星期

                    if (month.equals("*")) { //每个月
                        if (!day.equals("*") && !day.equals("?")) {
                            beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 30);//获取一个月
                        } else {
                            if (!week.equals("?")) {
                                beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 7);//获取每周
                            } else {
                                beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 1);//获取每天
                            }
                        }
                    } else {
                        if (month.contains("1") || month.contains("15") || month.contains("L")) { //获取半个月
                            beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 15);//获取半个月
                        } else { //获取一个月
                            beginDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 30);//获取一个月
                        }
                    }

                    beginDate = beginDate + " 00:00:00";

                    String endDate = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 0) + " 23:59:59";//结束时间

                    HttpServletResponse response = null;
                    IcApiauditreportQueryCriteria criteria = new IcApiauditreportQueryCriteria();
                    criteria.setDateStart(Timestamp.valueOf(beginDate));
                    criteria.setDateEnd(Timestamp.valueOf(endDate));
                    criteria.setExportNot(true);
                    //获取审计报告路径
                    // 根据申请部门名称分域
                    List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
                    tempPath = icApiauditreportService.downloadApiAuditReport(criteria, response,domainApiCodeList);

                    int sumRisk = icAlarmdisposalService.queryTotalNumberAlarms(beginDate, endDate);
                    int handledAlarm = icAlarmdisposalService.queryAlarmDisposalNum(beginDate, endDate, Const.INTERFACE_ALARM_DISPOSAL_PROCESSED);//已处置告警
                    int unhandledAlarm = icAlarmdisposalService.queryAlarmDisposalNum(beginDate, endDate, Const.INTERFACE_ALARM_DISPOSAL_UNHANDLED);//未处置告警
                    int ignoredAlarm = icAlarmdisposalService.queryAlarmDisposalNum(beginDate, endDate, Const.INTERFACE_ALARM_DISPOSAL_IGNORED);//已忽略告警
                    int hightRiskNum = icAlarmdisposalService.queryAlarmAmount(beginDate, endDate, Const.RISK_HIGH);//高危告警
                    int middleNum = icAlarmdisposalService.queryAlarmAmount(beginDate, endDate, Const.RISK_MIDDLE);//低位告警
                    //周期内共发现告警0个，已处置0个，未处置0个，已忽略0个。分为3级告警，其中高危告警0个，中危告警0个。详情请查看附件
                    content = "周期内共发现告警" + sumRisk + "个，已处置" + handledAlarm + "个，未处置" + unhandledAlarm + "个，已忽略" + ignoredAlarm + "个。分为3级告警，其中高危告警" + hightRiskNum + "个，中危告警" + middleNum + "个。详情请查看附件";
                } else if (Const.EMAIL_TITLETWO.equals(jobMotif)) {
                    String date = DateUtils.getYesterdayByCalendar("yyyy-MM-dd", 0);//时间
                    IcAlarmdisposalQueryCriteria criteria = new IcAlarmdisposalQueryCriteria();
                    criteria.setDateStart(Timestamp.valueOf(date + " 00:00:00"));
                    criteria.setDateEnd(Timestamp.valueOf(date + " 23:59:59"));
                    tempPath = icAlarmdisposalService.downloadMailExportExcel(icAlarmdisposalService.queryAll(criteria));
                    content = "周期内接口检测告警结果进行推送，详情请查看附件。";
                }

                // 封装
                MailAccount account = new MailAccount();
                account.setHost(jobHost);
                account.setPort(Integer.parseInt(jobPort));
                account.setAuth(true);
                try {

                    //AES解密
                    if (StringUtils.isNotEmpty(jobPass)) {
                        jobPass = AES.decrypt(jobPass, Const.AES_SECRET_KEY);
                    }
                    // 对称解密
                    account.setPass(jobPass);
                } catch (Exception e) {
                    throw new BadRequestException(e.getMessage());
                }
                account.setFrom(jobUser + "<" + jobFromUser + ">");
                // ssl方式发送
                account.setSslEnable(true);

                // 发送
                DataSource dataSource = new FileDataSource(new File(tempPath));
                int size = list.size();
                Mail.create(account)
                        .setTos(list.toArray(new String[size]))
                        .setTitle(interfaceAlarm)
                        .setContent(content)
                        .setHtml(true)
                        //关闭session
                        .setUseGlobalSession(false)
                        .setAttachments(dataSource)//附件
                        .send();

                File file = new File(tempPath);

                // 终止后删除临时文件
                boolean delete = file.delete();
                if (delete) {
                    log.info("邮件发送成功,已对本地临时文件进行删除");
                    resources.setExecutestate(Const.TASK_EXECUTESTATE_EXECUTE_SUCCESS);  // 执行成功
                }
            } else {
                throw new BadRequestException("请先启用该任务");
            }

        } catch (IOException e) {
            resources.setExecutestate(Const.TASK_EXECUTESTATE_EXECUTE_FAIL);
            e.printStackTrace();
        } finally {
            updateEmailSendConfig(resources);
            saveEmailRecord(resources, currentUserName);
        }
    }

    /**
     * 更新电子邮件发送配置
     *
     * @param resources 资源
     */
    public void updateEmailSendConfig(EmailSendConfig resources) {
        EmailSendConfig emailSendConfig = emailSendConfigRepository.findById(resources.getId()).orElseGet(EmailSendConfig::new);
        ValidationUtil.isNull(emailSendConfig.getId(), "EmailSendConfig", "id", resources.getId());
        emailSendConfig.copy(resources);
        emailSendConfigRepository.save(emailSendConfig);
    }

    /**
     * 保存电子邮件记录
     *
     * @param emailSendConfig 电子邮件发送配置
     */
    public void saveEmailRecord(EmailSendConfig emailSendConfig, String currentUserName) {
        EmailRecord emailRecord = new EmailRecord();
        emailRecord.setMotif(emailSendConfig.getMotif());
        emailRecord.setHost(emailSendConfig.getHost());
        emailRecord.setPort(emailSendConfig.getHost());
        emailRecord.setFromUser(emailSendConfig.getFromUser());
        emailRecord.setPass(emailSendConfig.getPass());
        emailRecord.setUsers(emailSendConfig.getUsers());
        emailRecord.setChecktime(emailSendConfig.getChecktime());
        emailRecord.setReceiver(emailSendConfig.getReceiver());
        emailRecord.setTaskstate(emailSendConfig.getTaskstate());
        emailRecord.setSubmittype(emailSendConfig.getSubmittype());
        emailRecord.setExecutestate(emailSendConfig.getExecutestate());
        if (emailSendConfig.getSubmittype().equals(Const.TASK_SUBMITTYPE_HAND)) {
            emailRecord.setCreateuser(currentUserName);
        } else {
            emailRecord.setCreateuser(Const.TASK_SUBMITTYPE_AUTOSUBMIT);
        }
        emailRecordRepository.save(emailRecord);
    }


}