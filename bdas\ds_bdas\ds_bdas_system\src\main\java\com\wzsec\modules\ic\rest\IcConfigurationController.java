package com.wzsec.modules.ic.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.IcConfiguration;
import com.wzsec.modules.ic.service.IcConfigurationService;
import com.wzsec.modules.ic.service.dto.IcConfigurationQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-07
 */
// @Api(tags = "黑白名单配置管理")
@RestController
@RequestMapping("/api/icConfiguration")
public class IcConfigurationController {

    private final IcConfigurationService icConfigurationService;

    public IcConfigurationController(IcConfigurationService icConfigurationService) {
        this.icConfigurationService = icConfigurationService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
//   // @PreAuthorize("@el.check('icConfiguration:list')")
    public void download(HttpServletResponse response, IcConfigurationQueryCriteria criteria) throws IOException {
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        icConfigurationService.download(icConfigurationService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询黑白名单配置")
    // @ApiOperation("查询黑白名单配置")
//   // @PreAuthorize("@el.check('icConfiguration:list')")
    public ResponseEntity<Object> getIcConfigurations(IcConfigurationQueryCriteria criteria, Pageable pageable) {
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        return new ResponseEntity<>(icConfigurationService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增黑白名单配置")
    // @ApiOperation("新增黑白名单配置")
    // @PreAuthorize("@el.check('icConfiguration:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody IcConfiguration resources) {
        return new ResponseEntity<>(icConfigurationService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改黑白名单配置")
    // @ApiOperation("修改黑白名单配置")
    // @PreAuthorize("@el.check('icConfiguration:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody IcConfiguration resources) {
        icConfigurationService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除黑白名单配置")
    // @ApiOperation("删除黑白名单配置")
    // @PreAuthorize("@el.check('icConfiguration:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        icConfigurationService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
