package com.wzsec.modules.ic.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.IcModelconfig;
import com.wzsec.modules.ic.service.IcModelconfigService;
import com.wzsec.modules.ic.service.dto.IcModelconfigQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-13
 */
// @Api(tags = "接口模型配置管理")
@RestController
@RequestMapping("/api/icModelconfig")
public class IcModelconfigController {

    private final IcModelconfigService icModelconfigService;

    public IcModelconfigController(IcModelconfigService icModelconfigService) {
        this.icModelconfigService = icModelconfigService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    // @PreAuthorize("@el.check('icModelconfig:list')")
    public void download(HttpServletResponse response, IcModelconfigQueryCriteria criteria) throws IOException {
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        icModelconfigService.download(icModelconfigService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询接口模型配置")
    // @ApiOperation("查询接口模型配置")
    // @PreAuthorize("@el.check('icModelconfig:list')")
    public ResponseEntity<Object> getIcModelconfigs(IcModelconfigQueryCriteria criteria, Pageable pageable) {
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        return new ResponseEntity<>(icModelconfigService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增接口模型配置")
    // @ApiOperation("新增接口模型配置")
    // @PreAuthorize("@el.check('icModelconfig:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody IcModelconfig resources) {
        return new ResponseEntity<>(icModelconfigService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改接口模型配置")
    // @ApiOperation("修改接口模型配置")
    // @PreAuthorize("@el.check('icModelconfig:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody IcModelconfig resources) {
        icModelconfigService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除接口模型配置")
    // @ApiOperation("删除接口模型配置")
    // @PreAuthorize("@el.check('icModelconfig:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        icModelconfigService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
