package com.wzsec.modules.ic.service.impl;

import com.wzsec.modules.ic.domain.ApiUnusedak;
import com.wzsec.modules.ic.repository.ApiUnusedakRepository;
import com.wzsec.modules.ic.service.ApiUnusedakService;
import com.wzsec.modules.ic.service.dto.ApiUnusedakDto;
import com.wzsec.modules.ic.service.dto.ApiUnusedakQueryCriteria;
import com.wzsec.modules.ic.service.mapper.ApiUnusedakMapper;
import com.wzsec.utils.FileUtil;
import com.wzsec.utils.PageUtil;
import com.wzsec.utils.QueryHelp;
import com.wzsec.utils.ValidationUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
//@CacheConfig(cacheNames = "apiUnusedak")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class ApiUnusedakServiceImpl implements ApiUnusedakService {

    private final ApiUnusedakRepository apiUnusedakRepository;

    private final ApiUnusedakMapper apiUnusedakMapper;

    public ApiUnusedakServiceImpl(ApiUnusedakRepository apiUnusedakRepository, ApiUnusedakMapper apiUnusedakMapper) {
        this.apiUnusedakRepository = apiUnusedakRepository;
        this.apiUnusedakMapper = apiUnusedakMapper;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(ApiUnusedakQueryCriteria criteria, Pageable pageable) {
        Page<ApiUnusedak> page = apiUnusedakRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(apiUnusedakMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<ApiUnusedakDto> queryAll(ApiUnusedakQueryCriteria criteria) {
        return apiUnusedakMapper.toDto(apiUnusedakRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
    }

    @Override
    //@Cacheable(key = "#p0")
    public ApiUnusedakDto findById(Integer id) {
        ApiUnusedak apiUnusedak = apiUnusedakRepository.findById(id).orElseGet(ApiUnusedak::new);
        ValidationUtil.isNull(apiUnusedak.getId(), "ApiUnusedak", "id", id);
        return apiUnusedakMapper.toDto(apiUnusedak);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public ApiUnusedakDto create(ApiUnusedak resources) {
        return apiUnusedakMapper.toDto(apiUnusedakRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(ApiUnusedak resources) {
        ApiUnusedak apiUnusedak = apiUnusedakRepository.findById(resources.getId()).orElseGet(ApiUnusedak::new);
        ValidationUtil.isNull(apiUnusedak.getId(), "ApiUnusedak", "id", resources.getId());
        apiUnusedak.copy(resources);
        apiUnusedakRepository.save(apiUnusedak);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            apiUnusedakRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ApiUnusedakDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ApiUnusedakDto apiUnusedak : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("调用方编码", apiUnusedak.getAk());
            map.put("调用方部门", apiUnusedak.getApplyorgname());
            map.put("接口编码", apiUnusedak.getApicode());
            map.put("接口名称", apiUnusedak.getApiname());
            map.put("接口URL", apiUnusedak.getApiurl());
            map.put("检测时间", apiUnusedak.getChecktime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public String queryLatestTime(String beginDate, String endDate) {
        return apiUnusedakRepository.queryLatestTime(beginDate, endDate);
    }

    @Override
    public String queryLatestTime(String beginDate, String endDate, List<String> domainApiCodeList) {
        return apiUnusedakRepository.queryLatestTime(beginDate, endDate, domainApiCodeList);
    }

    @Override
    public List<ApiUnusedak> queryLatestUnusedCheckResult(String beginDate, String endDate) {
        return apiUnusedakRepository.queryLatestUnusedCheckResult(beginDate, endDate);
    }

    @Override
    public int queryQuantity(String beginDate, String endDate) {
        return apiUnusedakRepository.queryQuantity(beginDate, endDate);
    }

    @Override
    public int queryQuantity(String beginDate, String endDate, List<String> domainApiCodeList) {
        return apiUnusedakRepository.queryQuantity(beginDate, endDate, domainApiCodeList);
    }
}