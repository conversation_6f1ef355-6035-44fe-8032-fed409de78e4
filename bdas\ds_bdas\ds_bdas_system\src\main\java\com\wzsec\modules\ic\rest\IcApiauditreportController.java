package com.wzsec.modules.ic.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.service.IcApiauditreportService;
import com.wzsec.modules.ic.service.dto.IcApiauditreportQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * API审计报告
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@RestController
@RequestMapping("/api/icApiauditreport")
public class IcApiauditreportController {

    private final IcApiauditreportService icApiauditreportService;

    public IcApiauditreportController(IcApiauditreportService icApiauditreportService) {
        this.icApiauditreportService = icApiauditreportService;
    }

    @GetMapping
    @Log("查询API审计报告")
    public ResponseEntity<Object> getIcApiauditreports(IcApiauditreportQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(null, HttpStatus.OK);
    }


    @Log("导出API审计报告")
    @GetMapping(value = "/downloadApiAuditReport")
    public void downloadApiAuditReport(HttpServletResponse response, IcApiauditreportQueryCriteria criteria) throws IOException {
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        icApiauditreportService.downloadApiAuditReport(criteria, response,domainApiCodeList);
    }
}
