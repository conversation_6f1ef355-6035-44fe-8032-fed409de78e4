package com.wzsec.modules.ic.rest;

import cn.hutool.core.lang.Console;
import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.ApiUnusedcheckresult;
import com.wzsec.modules.ic.service.ApiUnusedcheckresultService;
import com.wzsec.modules.ic.service.dto.ApiUnusedcheckresultQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
// @Api(tags = "僵尸API管理")
@RestController
@RequestMapping("/api/apiUnusedcheckresult")
public class ApiUnusedcheckresultController {

    private final ApiUnusedcheckresultService apiUnusedcheckresultService;

    public ApiUnusedcheckresultController(ApiUnusedcheckresultService apiUnusedcheckresultService) {
        this.apiUnusedcheckresultService = apiUnusedcheckresultService;
    }

    @Log("导出数据")
    @GetMapping(value = "/download")
    public void download(HttpServletResponse response, ApiUnusedcheckresultQueryCriteria criteria) throws IOException {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }

        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        apiUnusedcheckresultService.download(apiUnusedcheckresultService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询僵尸API")
    public ResponseEntity<Object> getApiUnusedcheckresults(ApiUnusedcheckresultQueryCriteria criteria, Pageable pageable) {
        try {
            Timestamp dateStart = criteria.getDateStart();
            Timestamp dateEnd = criteria.getDateEnd();
            List<String> checkTime = new ArrayList<>();
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateStart));
            checkTime.add(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateEnd));
            criteria.setChecktime(checkTime);
        } catch (Exception e) {
            Console.log("未填写查询时间");
        }
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        return new ResponseEntity<>(apiUnusedcheckresultService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增僵尸API")
    public ResponseEntity<Object> create(@Validated @RequestBody ApiUnusedcheckresult resources) {
        return new ResponseEntity<>(apiUnusedcheckresultService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改僵尸API")
    public ResponseEntity<Object> update(@Validated @RequestBody ApiUnusedcheckresult resources) {
        apiUnusedcheckresultService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除僵尸API")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        apiUnusedcheckresultService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
