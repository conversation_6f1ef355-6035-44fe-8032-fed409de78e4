package com.wzsec.modules.ic.repository;

import com.wzsec.modules.ic.domain.ApiInfrequentlycall;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
public interface ApiInfrequentlycallRepository extends JpaRepository<ApiInfrequentlycall, Integer>, JpaSpecificationExecutor<ApiInfrequentlycall> {

    /**
     * 查询内容结果(word导出取五条)
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiInfrequentlycall}>
     */
    @Query(value = "SELECT * FROM sdd_api_infrequentlycall WHERE checktime BETWEEN :beginDate AND :endDate ORDER BY checktime, callcount DESC LIMIT 5", nativeQuery = true)
    List<ApiInfrequentlycall> queryContentResult(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 查询最新时间
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link String}
     */
    @Query(value = "SELECT MAX(checktime) FROM sdd_api_infrequentlycall WHERE checktime BETWEEN :beginDate AND :endDate AND risk != '0'", nativeQuery = true)
    String queryLatestTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate);


    /**
     * 查询最新时间
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link String}
     */
    @Query(value = "SELECT MAX(checktime) FROM sdd_api_infrequentlycall WHERE checktime BETWEEN :beginDate AND :endDate AND risk != '0' AND apicode IN (:domainApiCodeList)", nativeQuery = true)
    String queryLatestTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);

    /**
     * 查询最近不常调用接口
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiInfrequentlycall}>
     */
    @Query(value = "SELECT DISTINCT(apicode) FROM sdd_api_infrequentlycall WHERE checktime BETWEEN :beginDate AND :endDate AND risk != '0' ORDER BY checktime, callcount DESC limit 5", nativeQuery = true)
    List<String> queryLatestInfrequentlyCall(@Param("beginDate") String beginDate, @Param("endDate") String endDate);


    /**
     * 查询最近不常调用接口
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return {@link List}<{@link ApiInfrequentlycall}>
     */
    @Query(value = "SELECT DISTINCT(apicode) FROM sdd_api_infrequentlycall WHERE checktime BETWEEN :beginDate AND :endDate AND risk != '0' AND apicode IN (:domainApiCodeList) ORDER BY checktime, callcount DESC limit 5", nativeQuery = true)
    List<String> queryLatestInfrequentlyCall(@Param("beginDate") String beginDate, @Param("endDate") String endDate, @Param("domainApiCodeList") List<String> domainApiCodeList);

}