package com.wzsec.modules.ic.rest;

import com.wzsec.aop.log.Log;
import com.wzsec.modules.ic.domain.IcPortrait;
import com.wzsec.modules.ic.service.IcPortraitService;
import com.wzsec.modules.ic.service.dto.IcPortraitQueryCriteria;
import com.wzsec.utils.DomainUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-19
 */
// @Api(tags = "API画像管理")
@RestController
@RequestMapping("/api/icPortrait")
public class IcPortraitController {

    private final IcPortraitService icPortraitService;

    public IcPortraitController(IcPortraitService icPortraitService) {
        this.icPortraitService = icPortraitService;
    }

    @Log("导出数据")
    // @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    // @PreAuthorize("@el.check('icPortrait:download')")
    public void download(HttpServletResponse response, IcPortraitQueryCriteria criteria) throws IOException {
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        icPortraitService.download(icPortraitService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询API画像")
    // @ApiOperation("查询API画像")
    // @PreAuthorize("@el.check('icPortrait:list')")
    public ResponseEntity<Object> getIcPortraits(IcPortraitQueryCriteria criteria, Pageable pageable) {
        // 根据申请部门名称分域
        List<String> domainApiCodeList = DomainUtil.queryDomainApiCode();
        criteria.setApicode(domainApiCodeList);
        return new ResponseEntity<>(icPortraitService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增API画像")
    // @ApiOperation("新增API画像")
    // @PreAuthorize("@el.check('icPortrait:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody IcPortrait resources) {
        return new ResponseEntity<>(icPortraitService.create(resources), HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改API画像")
    // @ApiOperation("修改API画像")
    // @PreAuthorize("@el.check('icPortrait:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody IcPortrait resources) {
        icPortraitService.update(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除API画像")
    // @ApiOperation("删除API画像")
    // @PreAuthorize("@el.check('icPortrait:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Integer[] ids) {
        icPortraitService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("查询数源单位")
    @PostMapping(value = "/queryOrganizations")
    public ResponseEntity<Object> queryOrganizations() {
        return new ResponseEntity<>(icPortraitService.queryOrganizations(), HttpStatus.OK);
    }

    @Log("查询注册部门")
    @PostMapping(value = "/queryRegisterUnits")
    public ResponseEntity<Object> queryRegisterUnits() {
        return new ResponseEntity<>(icPortraitService.queryRegisterUnits(), HttpStatus.OK);
    }


}
