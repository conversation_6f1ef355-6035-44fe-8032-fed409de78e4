package com.wzsec.modules.ic.service;

import com.wzsec.modules.ic.domain.ApiUnusedcheckresult;
import com.wzsec.modules.ic.service.dto.ApiUnusedcheckresultDto;
import com.wzsec.modules.ic.service.dto.ApiUnusedcheckresultQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
public interface ApiUnusedcheckresultService {

    /**
     * 查询数据分页
     *
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(ApiUnusedcheckresultQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 条件参数
     * @return List<ApiUnusedcheckresultDto>
     */
    List<ApiUnusedcheckresultDto> queryAll(ApiUnusedcheckresultQueryCriteria criteria);

    /**
     * 根据ID查询
     *
     * @param id ID
     * @return ApiUnusedcheckresultDto
     */
    ApiUnusedcheckresultDto findById(Integer id);

    /**
     * 创建
     *
     * @param resources /
     * @return ApiUnusedcheckresultDto
     */
    ApiUnusedcheckresultDto create(ApiUnusedcheckresult resources);

    /**
     * 编辑
     *
     * @param resources /
     */
    void update(ApiUnusedcheckresult resources);

    /**
     * 多选删除
     *
     * @param ids /
     */
    void deleteAll(Integer[] ids);

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<ApiUnusedcheckresultDto> all, HttpServletResponse response) throws IOException;


    /**
     * 查询最新时间
     *
     * @param beginDate    开始日期
     * @param endDate      结束日期
     * @return {@link String}
     */
    String queryLatestTime(String beginDate, String endDate);

    /**
     * 查询最新时间
     *
     * @param beginDate    开始日期
     * @param endDate      结束日期
     * @return {@link String}
     */
    String queryLatestTime(String beginDate, String endDate, List<String> domainApiCodeList);

    /**
     * 查询最新时间
     *
     * @return {@link String}
     */
    String queryLatestTime();

    /**
     * 查询最近失活API
     *
     * @param beginDate    开始日期
     * @param endDate      结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    List<String> queryLatestUnusedCheckResults(String beginDate, String endDate);


    /**
     * 查询最近失活API
     *
     * @param beginDate    开始日期
     * @param endDate      结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    List<ApiUnusedcheckresult> getinaliveapi(String beginDate, String endDate);


    /**
     * 查询最近失活API
     *
     * @param beginDate    开始日期
     * @param endDate      结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    List<ApiUnusedcheckresult> queryLatestUnusedCheckResult(String beginDate, String endDate);


    /**
     * 查询最近失活API
     *
     * @param beginDate    开始日期
     * @param endDate      结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    List<ApiUnusedcheckresult> queryLatestUnusedCheckResult(String beginDate, String endDate,List<String> domainApiCodeList);

    /**
     * 查询最近失活API
     *
     * @param beginDate    开始日期
     * @param endDate      结束日期
     * @return {@link List}<{@link ApiUnusedcheckresult}>
     */
    int queryQuantity(String beginDate, String endDate);
}