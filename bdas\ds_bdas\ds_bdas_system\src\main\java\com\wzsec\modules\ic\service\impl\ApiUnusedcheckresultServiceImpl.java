package com.wzsec.modules.ic.service.impl;

import com.wzsec.modules.ic.domain.ApiUnusedcheckresult;
import com.wzsec.modules.ic.domain.IcInterfaceInfo;
import com.wzsec.modules.ic.repository.ApiUnusedcheckresultRepository;
import com.wzsec.modules.ic.service.ApiUnusedcheckresultService;
import com.wzsec.modules.ic.service.IcInterfaceInfoService;
import com.wzsec.modules.ic.service.dto.ApiUnusedcheckresultDto;
import com.wzsec.modules.ic.service.dto.ApiUnusedcheckresultQueryCriteria;
import com.wzsec.modules.ic.service.mapper.ApiUnusedcheckresultMapper;
import com.wzsec.utils.FileUtil;
import com.wzsec.utils.PageUtil;
import com.wzsec.utils.QueryHelp;
import com.wzsec.utils.ValidationUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-05-19
 */
@Service
//@CacheConfig(cacheNames = "apiUnusedcheckresult")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class ApiUnusedcheckresultServiceImpl implements ApiUnusedcheckresultService {

    private final ApiUnusedcheckresultRepository apiUnusedcheckresultRepository;

    private final ApiUnusedcheckresultMapper apiUnusedcheckresultMapper;

    private final IcInterfaceInfoService icInterfaceInfoService;

    public ApiUnusedcheckresultServiceImpl(ApiUnusedcheckresultRepository apiUnusedcheckresultRepository,
                                           ApiUnusedcheckresultMapper apiUnusedcheckresultMapper,
                                           IcInterfaceInfoService icInterfaceInfoService) {
        this.apiUnusedcheckresultRepository = apiUnusedcheckresultRepository;
        this.apiUnusedcheckresultMapper = apiUnusedcheckresultMapper;
        this.icInterfaceInfoService = icInterfaceInfoService;
    }

    @Override
    //@Cacheable
    public Map<String, Object> queryAll(ApiUnusedcheckresultQueryCriteria criteria, Pageable pageable) {
        Map<String, IcInterfaceInfo> fullInterface = icInterfaceInfoService.findFullInterface();
        Page<ApiUnusedcheckresult> page = apiUnusedcheckresultRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder), pageable);
        return PageUtil.toPage(page.map(apiUnusedcheckresultMapper::toDto));
    }

    @Override
    //@Cacheable
    public List<ApiUnusedcheckresultDto> queryAll(ApiUnusedcheckresultQueryCriteria criteria) {
        Map<String, IcInterfaceInfo> fullInterface = icInterfaceInfoService.findFullInterface();
        List<ApiUnusedcheckresultDto> dto = apiUnusedcheckresultMapper.toDto(apiUnusedcheckresultRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root, criteria, criteriaBuilder)));
        return dto;
    }

    @Override
    //@Cacheable(key = "#p0")
    public ApiUnusedcheckresultDto findById(Integer id) {
        ApiUnusedcheckresult apiUnusedcheckresult = apiUnusedcheckresultRepository.findById(id).orElseGet(ApiUnusedcheckresult::new);
        ValidationUtil.isNull(apiUnusedcheckresult.getId(), "ApiUnusedcheckresult", "id", id);
        return apiUnusedcheckresultMapper.toDto(apiUnusedcheckresult);
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public ApiUnusedcheckresultDto create(ApiUnusedcheckresult resources) {
        return apiUnusedcheckresultMapper.toDto(apiUnusedcheckresultRepository.save(resources));
    }

    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public void update(ApiUnusedcheckresult resources) {
        ApiUnusedcheckresult apiUnusedcheckresult = apiUnusedcheckresultRepository.findById(resources.getId()).orElseGet(ApiUnusedcheckresult::new);
        ValidationUtil.isNull(apiUnusedcheckresult.getId(), "ApiUnusedcheckresult", "id", resources.getId());
        apiUnusedcheckresult.copy(resources);
        apiUnusedcheckresultRepository.save(apiUnusedcheckresult);
    }

    @Override
    //@CacheEvict(allEntries = true)
    public void deleteAll(Integer[] ids) {
        for (Integer id : ids) {
            apiUnusedcheckresultRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<ApiUnusedcheckresultDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        int size = 0;
        for (ApiUnusedcheckresultDto apiUnusedcheckresult : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("接口编码", apiUnusedcheckresult.getApicode());
            map.put("接口URL", apiUnusedcheckresult.getApiurl());
            map.put("接口名称", apiUnusedcheckresult.getApiname());
            map.put("检测时间", apiUnusedcheckresult.getChecktime());
            list.add(map);
            size = map.size();
        }
        FileUtil.downloadExcel(list, response, "失活API检测结果", size);
    }

    @Override
    public String queryLatestTime(String beginDate, String endDate) {
        return apiUnusedcheckresultRepository.queryLatestTime(beginDate, endDate);
    }

    @Override
    public String queryLatestTime(String beginDate, String endDate, List<String> domainApiCodeList) {
        return apiUnusedcheckresultRepository.queryLatestTime(beginDate, endDate, domainApiCodeList);
    }

    @Override
    public String queryLatestTime() {
        return apiUnusedcheckresultRepository.queryLatestTime();
    }

    @Override
    public List<String> queryLatestUnusedCheckResults(String beginDate, String endDate) {
        return apiUnusedcheckresultRepository.queryLatestUnusedCheckResults(beginDate, endDate);
    }

    @Override
    public List<ApiUnusedcheckresult> getinaliveapi(String beginDate, String endDate) {
        return apiUnusedcheckresultRepository.getinaliveapi(beginDate, endDate);
    }

    @Override
    public List<ApiUnusedcheckresult> queryLatestUnusedCheckResult(String beginDate, String endDate) {
        return apiUnusedcheckresultRepository.queryLatestUnusedCheckResult(beginDate, endDate);
    }

    @Override
    public List<ApiUnusedcheckresult> queryLatestUnusedCheckResult(String beginDate, String endDate, List<String> domainApiCodeList) {
        return apiUnusedcheckresultRepository.queryLatestUnusedCheckResult(beginDate, endDate, domainApiCodeList);
    }

    @Override
    public int queryQuantity(String beginDate, String endDate) {
        return apiUnusedcheckresultRepository.queryQuantity(beginDate, endDate);
    }
}