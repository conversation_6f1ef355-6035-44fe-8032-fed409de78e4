package com.wzsec.modules.ic.service;

import com.wzsec.modules.ic.service.dto.IcApiauditreportQueryCriteria;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-27
 */
public interface IcApiauditreportService {

    /**
     * 导出API审计报告
     *
     * @param response 响应
     * @param criteria 标准
     * @throws IOException ioexception
     */
    String downloadApiAuditReport(IcApiauditreportQueryCriteria criteria, HttpServletResponse response, List<String> domainApiCodeList) throws IOException;
}